"""
<PERSON><PERSON><PERSON><PERSON><PERSON>n Diện Hình Ảnh
Xử lý việc khớp mẫu dựa trên OpenCV cho tự động hóa Android
"""

import cv2
import numpy as np
from typing import List, Tuple, Optional
from PIL import Image
import os


class ImageMatcher:
    """Xử lý việc khớp mẫu hình ảnh bằng OpenCV"""

    def __init__(self, confidence_threshold: float = 0.8):
        self.confidence_threshold = confidence_threshold  # Ngưỡng độ tin cậy

    def find_template(self, screenshot: Image.Image, template_path: str,
                     confidence: Optional[float] = None) -> Optional[Tuple[int, int, float]]:
        """
        Tìm hình ảnh mẫu trong ảnh chụp màn hình

        Args:
            screenshot: Ảnh PIL của màn hình
            template_path: Đường dẫn đến file ảnh mẫu
            confidence: Ghi đè ngưỡng độ tin cậy mặc định

        Returns:
            Tuple (x, y, confidence) nếu tìm thấy, None nếu không
        """
        if not os.path.exists(template_path):
            print(f"Template file not found: {template_path}")
            return None
            
        confidence_thresh = confidence or self.confidence_threshold
        
        try:
            # Convert PIL to OpenCV format
            screenshot_cv = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
            template_cv = cv2.imread(template_path, cv2.IMREAD_COLOR)
            
            if template_cv is None:
                print(f"Could not load template: {template_path}")
                return None
            
            # Perform template matching
            result = cv2.matchTemplate(screenshot_cv, template_cv, cv2.TM_CCOEFF_NORMED)
            min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
            
            if max_val >= confidence_thresh:
                # Calculate center coordinates
                template_h, template_w = template_cv.shape[:2]
                center_x = max_loc[0] + template_w // 2
                center_y = max_loc[1] + template_h // 2
                
                return (center_x, center_y, max_val)
            else:
                print(f"Template not found with confidence {max_val:.3f} < {confidence_thresh}")
                return None
                
        except Exception as e:
            print(f"Error in template matching: {e}")
            return None
    
    def find_all_templates(self, screenshot: Image.Image, template_path: str,
                          confidence: Optional[float] = None) -> List[Tuple[int, int, float]]:
        """
        Find all instances of template in screenshot
        
        Returns:
            List of (x, y, confidence) tuples for all matches
        """
        if not os.path.exists(template_path):
            return []
            
        confidence_thresh = confidence or self.confidence_threshold
        matches = []
        
        try:
            screenshot_cv = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
            template_cv = cv2.imread(template_path, cv2.IMREAD_COLOR)
            
            if template_cv is None:
                return []
            
            result = cv2.matchTemplate(screenshot_cv, template_cv, cv2.TM_CCOEFF_NORMED)
            template_h, template_w = template_cv.shape[:2]
            
            # Find all locations above threshold
            locations = np.where(result >= confidence_thresh)
            
            for pt in zip(*locations[::-1]):  # Switch x and y
                center_x = pt[0] + template_w // 2
                center_y = pt[1] + template_h // 2
                confidence_val = result[pt[1], pt[0]]
                matches.append((center_x, center_y, confidence_val))
            
            # Remove overlapping matches (non-maximum suppression)
            matches = self._remove_overlapping_matches(matches, template_w, template_h)
            
            return matches
            
        except Exception as e:
            print(f"Error finding all templates: {e}")
            return []
    
    def _remove_overlapping_matches(self, matches: List[Tuple[int, int, float]], 
                                   template_w: int, template_h: int) -> List[Tuple[int, int, float]]:
        """Remove overlapping matches using simple distance-based filtering"""
        if len(matches) <= 1:
            return matches
            
        # Sort by confidence (highest first)
        matches.sort(key=lambda x: x[2], reverse=True)
        
        filtered_matches = []
        min_distance = max(template_w, template_h) * 0.5  # Minimum distance between matches
        
        for match in matches:
            x, y, conf = match
            is_overlapping = False
            
            for existing_match in filtered_matches:
                ex, ey, _ = existing_match
                distance = np.sqrt((x - ex)**2 + (y - ey)**2)
                
                if distance < min_distance:
                    is_overlapping = True
                    break
            
            if not is_overlapping:
                filtered_matches.append(match)
        
        return filtered_matches
    
    def save_match_visualization(self, screenshot: Image.Image, template_path: str, 
                               matches: List[Tuple[int, int, float]], output_path: str):
        """Save screenshot with match locations highlighted"""
        try:
            screenshot_cv = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
            template_cv = cv2.imread(template_path, cv2.IMREAD_COLOR)
            
            if template_cv is None:
                return
                
            template_h, template_w = template_cv.shape[:2]
            
            for x, y, conf in matches:
                # Draw rectangle around match
                top_left = (x - template_w // 2, y - template_h // 2)
                bottom_right = (x + template_w // 2, y + template_h // 2)
                
                cv2.rectangle(screenshot_cv, top_left, bottom_right, (0, 255, 0), 2)
                
                # Add confidence text
                cv2.putText(screenshot_cv, f"{conf:.2f}", 
                           (top_left[0], top_left[1] - 10),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)
            
            cv2.imwrite(output_path, screenshot_cv)
            
        except Exception as e:
            print(f"Error saving visualization: {e}")
    
    def crop_region(self, screenshot: Image.Image, x: int, y: int, 
                   width: int, height: int) -> Image.Image:
        """Crop a specific region from screenshot"""
        left = max(0, x - width // 2)
        top = max(0, y - height // 2)
        right = min(screenshot.width, x + width // 2)
        bottom = min(screenshot.height, y + height // 2)
        
        return screenshot.crop((left, top, right, bottom))
    
    def preprocess_image(self, image: Image.Image, 
                        resize_factor: Optional[float] = None,
                        grayscale: bool = False) -> Image.Image:
        """Preprocess image for better matching"""
        processed = image.copy()
        
        if resize_factor and resize_factor != 1.0:
            new_width = int(processed.width * resize_factor)
            new_height = int(processed.height * resize_factor)
            processed = processed.resize((new_width, new_height), Image.Resampling.LANCZOS)
        
        if grayscale:
            processed = processed.convert('L')
            
        return processed
