#!/usr/bin/env python3
"""
Android Automation Bot - Main Entry Point
🤖 Bot Tự Động Tác Vụ Android

A Python application for automating repetitive tasks on Android devices
using ADB and OpenCV image recognition.
"""

import sys
import os
import logging
import argparse
from pathlib import Path

# Add src directory to Python path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

from src.gui.main_window import MainWindow
from src.task_scheduler import setup_logging


def check_dependencies():
    """Check if required dependencies are available"""
    missing_deps = []
    
    try:
        import cv2
    except ImportError:
        missing_deps.append("opencv-python")
    
    try:
        import numpy
    except ImportError:
        missing_deps.append("numpy")
    
    try:
        from PIL import Image
    except ImportError:
        missing_deps.append("Pillow")
    
    # Check if ADB is available
    import subprocess
    try:
        subprocess.run(['adb', 'version'], capture_output=True, check=True)
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("⚠️  Warning: ADB (Android Debug Bridge) not found in PATH")
        print("   Please install Android SDK Platform Tools and add to PATH")
        print("   Download: https://developer.android.com/studio/releases/platform-tools")
    
    if missing_deps:
        print("❌ Missing required dependencies:")
        for dep in missing_deps:
            print(f"   - {dep}")
        print("\n📦 Install missing dependencies with:")
        print(f"   pip install {' '.join(missing_deps)}")
        return False
    
    return True


def setup_directories():
    """Create necessary directories"""
    directories = [
        "config",
        "config/templates",
        "config/templates/common",
        "logs",
        "debug_screenshots"
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)


def main():
    """Main application entry point"""
    parser = argparse.ArgumentParser(
        description="Android Automation Bot - Automate repetitive Android tasks"
    )
    parser.add_argument(
        "--debug", 
        action="store_true", 
        help="Enable debug logging"
    )
    parser.add_argument(
        "--config-dir", 
        default="config",
        help="Configuration directory path (default: config)"
    )
    
    args = parser.parse_args()
    
    # Setup logging
    log_level = logging.DEBUG if args.debug else logging.INFO
    setup_logging()
    
    # Set log level
    logging.getLogger().setLevel(log_level)
    
    logger = logging.getLogger(__name__)
    logger.info("🤖 Starting Android Automation Bot")
    
    # Check dependencies
    if not check_dependencies():
        print("\n❌ Please install missing dependencies before running the application.")
        sys.exit(1)
    
    # Setup directories
    setup_directories()
    
    # In thông tin khởi động
    print("🤖 Bot Tự Động Tác Vụ Android")
    print("=" * 50)
    print("📱 Tính năng:")
    print("  • Quản lý thiết bị ADB")
    print("  • Nhận diện hình ảnh OpenCV")
    print("  • Lên lịch tác vụ với đếm ngược")
    print("  • Hỗ trợ đa thiết bị")
    print("  • Tự động đóng quảng cáo")
    print("  • Giao diện 2 cột trực quan")
    print()
    print("🔧 Hướng dẫn thiết lập:")
    print("  1. Bật USB Debugging trên thiết bị Android")
    print("  2. Kết nối thiết bị qua USB hoặc WiFi ADB")
    print("  3. Nhấn 'Refresh Devices' để phát hiện thiết bị")
    print("  4. Thêm app và cấu hình tác vụ tự động")
    print("  5. Nhấn 'Start Scheduler' để bắt đầu tự động hóa")
    print()
    print("📚 Xem thêm: README.md và HUONG_DAN_SU_DUNG.md")
    print("🧪 Kiểm tra hệ thống: python test_bot.py")
    print("=" * 50)
    
    try:
        # Start the GUI application
        app = MainWindow()
        logger.info("GUI application started")
        app.run()
        
    except KeyboardInterrupt:
        logger.info("Application interrupted by user")
        print("\n👋 Application stopped by user")
        
    except Exception as e:
        logger.error(f"Application error: {e}", exc_info=True)
        print(f"\n❌ Application error: {e}")
        sys.exit(1)
    
    finally:
        logger.info("🤖 Android Automation Bot stopped")
        print("👋 Goodbye!")


if __name__ == "__main__":
    main()
