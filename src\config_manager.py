"""
Configuration Manager Mo<PERSON>le
Handles app configurations, settings, and data persistence
"""

import json
import os
import time
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta


@dataclass
class AppTask:
    """Represents a single automation task for an app"""
    name: str
    package_name: str
    template_images: List[str]  # List of template image paths
    interval_seconds: int
    enabled: bool = True
    last_run: Optional[float] = None  # Unix timestamp
    next_run: Optional[float] = None  # Unix timestamp
    success_count: int = 0
    error_count: int = 0
    last_error: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization"""
        return asdict(self)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'AppTask':
        """Create AppTask from dictionary"""
        return cls(**data)
    
    def update_last_run(self):
        """Update last run time and calculate next run"""
        now = time.time()
        self.last_run = now
        self.next_run = now + self.interval_seconds
    
    def is_ready_to_run(self) -> bool:
        """Check if task is ready to run"""
        if not self.enabled:
            return False
            
        if self.next_run is None:
            return True  # Never run before
            
        return time.time() >= self.next_run
    
    def get_time_until_next_run(self) -> int:
        """Get seconds until next run (0 if ready)"""
        if not self.enabled or self.next_run is None:
            return 0
            
        remaining = int(self.next_run - time.time())
        return max(0, remaining)
    
    def format_countdown(self) -> str:
        """Format countdown time as human-readable string"""
        if not self.enabled:
            return "Tạm dừng"
            
        if self.next_run is None:
            return "Chạy ngay"
            
        remaining = self.get_time_until_next_run()
        if remaining <= 0:
            return "Chạy ngay"
            
        hours = remaining // 3600
        minutes = (remaining % 3600) // 60
        seconds = remaining % 60
        
        if hours > 0:
            return f"còn {hours}h{minutes:02d}m"
        elif minutes > 0:
            return f"còn {minutes}m{seconds:02d}s"
        else:
            return f"còn {seconds}s"


class ConfigManager:
    """Manages application configuration and task persistence"""
    
    def __init__(self, config_dir: str = "config"):
        self.config_dir = config_dir
        self.tasks_file = os.path.join(config_dir, "tasks.json")
        self.settings_file = os.path.join(config_dir, "settings.json")
        self.templates_dir = os.path.join(config_dir, "templates")
        
        # Create directories if they don't exist
        os.makedirs(config_dir, exist_ok=True)
        os.makedirs(self.templates_dir, exist_ok=True)
        
        self.tasks: Dict[str, AppTask] = {}
        self.settings: Dict[str, Any] = {}
        
        self.load_config()
    
    def load_config(self):
        """Load configuration from files"""
        self.load_tasks()
        self.load_settings()
    
    def load_tasks(self):
        """Load app tasks from JSON file"""
        if os.path.exists(self.tasks_file):
            try:
                with open(self.tasks_file, 'r', encoding='utf-8') as f:
                    tasks_data = json.load(f)
                
                self.tasks = {}
                for task_id, task_data in tasks_data.items():
                    self.tasks[task_id] = AppTask.from_dict(task_data)
                    
            except Exception as e:
                print(f"Error loading tasks: {e}")
                self.tasks = {}
        else:
            self.tasks = {}
    
    def save_tasks(self):
        """Save app tasks to JSON file"""
        try:
            tasks_data = {}
            for task_id, task in self.tasks.items():
                tasks_data[task_id] = task.to_dict()
            
            with open(self.tasks_file, 'w', encoding='utf-8') as f:
                json.dump(tasks_data, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            print(f"Error saving tasks: {e}")
    
    def load_settings(self):
        """Load application settings"""
        default_settings = {
            "default_confidence": 0.8,
            "screenshot_delay": 1.0,
            "max_retries": 3,
            "log_level": "INFO",
            "auto_close_ads": True,
            "ui_update_interval": 1000,  # milliseconds
            "selected_device": None
        }
        
        if os.path.exists(self.settings_file):
            try:
                with open(self.settings_file, 'r', encoding='utf-8') as f:
                    loaded_settings = json.load(f)
                    default_settings.update(loaded_settings)
            except Exception as e:
                print(f"Error loading settings: {e}")
        
        self.settings = default_settings
    
    def save_settings(self):
        """Save application settings"""
        try:
            with open(self.settings_file, 'w', encoding='utf-8') as f:
                json.dump(self.settings, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"Error saving settings: {e}")
    
    def add_task(self, task: AppTask) -> str:
        """Add a new task and return its ID"""
        task_id = f"{task.package_name}_{int(time.time())}"
        self.tasks[task_id] = task
        self.save_tasks()
        return task_id
    
    def update_task(self, task_id: str, task: AppTask):
        """Update an existing task"""
        if task_id in self.tasks:
            self.tasks[task_id] = task
            self.save_tasks()
    
    def remove_task(self, task_id: str):
        """Remove a task"""
        if task_id in self.tasks:
            del self.tasks[task_id]
            self.save_tasks()
    
    def get_task(self, task_id: str) -> Optional[AppTask]:
        """Get a specific task"""
        return self.tasks.get(task_id)
    
    def get_all_tasks(self) -> Dict[str, AppTask]:
        """Get all tasks"""
        return self.tasks.copy()
    
    def get_ready_tasks(self) -> List[tuple]:
        """Get tasks that are ready to run"""
        ready_tasks = []
        for task_id, task in self.tasks.items():
            if task.is_ready_to_run():
                ready_tasks.append((task_id, task))
        return ready_tasks
    
    def get_tasks_sorted_by_next_run(self) -> List[tuple]:
        """Get tasks sorted by next run time"""
        task_list = [(task_id, task) for task_id, task in self.tasks.items()]
        
        def sort_key(item):
            task_id, task = item
            if not task.enabled:
                return float('inf')  # Disabled tasks go to end
            if task.next_run is None:
                return 0  # Ready to run immediately
            return task.next_run
        
        return sorted(task_list, key=sort_key)
    
    def get_template_path(self, app_name: str, template_name: str) -> str:
        """Get full path for a template image"""
        app_dir = os.path.join(self.templates_dir, app_name)
        return os.path.join(app_dir, template_name)
    
    def create_app_template_dir(self, app_name: str):
        """Create template directory for an app"""
        app_dir = os.path.join(self.templates_dir, app_name)
        os.makedirs(app_dir, exist_ok=True)
        return app_dir
    
    def get_app_templates(self, app_name: str) -> List[str]:
        """Get list of template files for an app"""
        app_dir = os.path.join(self.templates_dir, app_name)
        if not os.path.exists(app_dir):
            return []
        
        templates = []
        for file in os.listdir(app_dir):
            if file.lower().endswith(('.png', '.jpg', '.jpeg')):
                templates.append(file)
        
        return sorted(templates)
    
    def get_setting(self, key: str, default=None):
        """Get a setting value"""
        return self.settings.get(key, default)
    
    def set_setting(self, key: str, value: Any):
        """Set a setting value"""
        self.settings[key] = value
        self.save_settings()
