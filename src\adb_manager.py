"""
<PERSON><PERSON><PERSON><PERSON><PERSON> Lý ADB
Xử lý kết nối, đi<PERSON>u khiển và tương tác với thiết bị Android qua ADB
"""

import subprocess
import time
import os
from typing import List, Optional
from PIL import Image
import io


class ADBManager:
    """Quản lý kết nối ADB và tương tác với thiết bị Android"""

    def __init__(self, adb_path: str = "adb"):
        self.connected_devices = []  # Danh sách thiết bị đã kết nối
        self.current_device = None   # Thiết bị hiện tại được chọn
        self.adb_path = adb_path     # Đường dẫn đến ADB executable

    def set_adb_path(self, adb_path: str):
        """Thiết lập đường dẫn đến ADB executable"""
        self.adb_path = adb_path

    def test_adb_connection(self) -> bool:
        """Test if ADB is working properly"""
        try:
            if not self.adb_path or not os.path.exists(self.adb_path):
                return False

            # Test ADB version
            result = subprocess.run([self.adb_path, 'version'],
                                  capture_output=True, text=True, check=True, timeout=5)

            return 'Android Debug Bridge' in result.stdout

        except Exception:
            return False

    def get_connected_devices(self) -> List[str]:
        """Lấy danh sách thiết bị Android đã kết nối"""
        if not self.adb_path:
            raise Exception("Chưa thiết lập đường dẫn ADB")

        if not os.path.exists(self.adb_path):
            raise Exception(f"File ADB không tồn tại: {self.adb_path}")

        try:
            # Test if ADB is working
            result = subprocess.run([self.adb_path, 'version'],
                                  capture_output=True, text=True, check=True, timeout=10)

            if 'Android Debug Bridge' not in result.stdout:
                raise Exception("File được chọn không phải ADB hợp lệ")

            # Get devices list
            result = subprocess.run([self.adb_path, 'devices'],
                                  capture_output=True, text=True, check=True, timeout=10)
            lines = result.stdout.strip().split('\n')[1:]  # Bỏ qua dòng tiêu đề
            devices = []

            for line in lines:
                if line.strip() and '\tdevice' in line:
                    device_id = line.split('\t')[0]
                    devices.append(device_id)

            self.connected_devices = devices
            return devices

        except subprocess.TimeoutExpired:
            raise Exception("ADB không phản hồi (timeout)")
        except subprocess.CalledProcessError as e:
            raise Exception(f"Lỗi khi chạy ADB: {e}")
        except FileNotFoundError:
            raise Exception(f"Không thể chạy file ADB: {self.adb_path}")
        except Exception as e:
            raise Exception(f"Lỗi không xác định: {e}")

    def select_device(self, device_id: str) -> bool:
        """Chọn một thiết bị cụ thể để thực hiện các thao tác"""
        if device_id in self.connected_devices:
            self.current_device = device_id
            return True
        return False

    def take_screenshot(self, save_path: Optional[str] = None) -> Optional[Image.Image]:
        """Chụp ảnh màn hình của thiết bị hiện tại"""
        if not self.current_device:
            print("Chưa chọn thiết bị")
            return None

        try:
            # Chụp ảnh màn hình bằng ADB
            result = subprocess.run([
                self.adb_path, '-s', self.current_device, 'exec-out',
                'screencap', '-p'
            ], capture_output=True, check=True)

            # Chuyển đổi sang PIL Image
            image = Image.open(io.BytesIO(result.stdout))

            if save_path:
                image.save(save_path)

            return image

        except subprocess.CalledProcessError as e:
            print(f"Lỗi khi chụp ảnh màn hình: {e}")
            return None

    def tap(self, x: int, y: int) -> bool:
        """Chạm tại tọa độ cụ thể"""
        if not self.current_device:
            print("Chưa chọn thiết bị")
            return False

        try:
            subprocess.run([
                self.adb_path, '-s', self.current_device, 'shell',
                'input', 'tap', str(x), str(y)
            ], check=True)
            return True

        except subprocess.CalledProcessError as e:
            print(f"Lỗi khi chạm tại ({x}, {y}): {e}")
            return False
    
    def swipe(self, x1: int, y1: int, x2: int, y2: int, duration: int = 300) -> bool:
        """Swipe from (x1,y1) to (x2,y2)"""
        if not self.current_device:
            print("No device selected")
            return False
            
        try:
            subprocess.run([
                self.adb_path, '-s', self.current_device, 'shell',
                'input', 'swipe', str(x1), str(y1), str(x2), str(y2), str(duration)
            ], check=True)
            return True

        except subprocess.CalledProcessError as e:
            print(f"Error swiping: {e}")
            return False
    
    def launch_app(self, package_name: str) -> bool:
        """Launch an Android app by package name"""
        if not self.current_device:
            print("No device selected")
            return False
            
        try:
            subprocess.run([
                self.adb_path, '-s', self.current_device, 'shell',
                'monkey', '-p', package_name, '-c', 'android.intent.category.LAUNCHER', '1'
            ], check=True)
            time.sleep(2)  # Wait for app to launch
            return True

        except subprocess.CalledProcessError as e:
            print(f"Error launching app {package_name}: {e}")
            return False
    
    def press_back(self) -> bool:
        """Press back button"""
        if not self.current_device:
            return False
            
        try:
            subprocess.run([
                self.adb_path, '-s', self.current_device, 'shell',
                'input', 'keyevent', 'KEYCODE_BACK'
            ], check=True)
            return True

        except subprocess.CalledProcessError as e:
            print(f"Error pressing back: {e}")
            return False
    
    def press_home(self) -> bool:
        """Press home button"""
        if not self.current_device:
            return False
            
        try:
            subprocess.run([
                self.adb_path, '-s', self.current_device, 'shell',
                'input', 'keyevent', 'KEYCODE_HOME'
            ], check=True)
            return True

        except subprocess.CalledProcessError as e:
            print(f"Error pressing home: {e}")
            return False
    
    def get_device_info(self) -> dict:
        """Get device information"""
        if not self.current_device:
            return {}
            
        try:
            # Get device model
            model_result = subprocess.run([
                self.adb_path, '-s', self.current_device, 'shell',
                'getprop', 'ro.product.model'
            ], capture_output=True, text=True, check=True)

            # Get Android version
            version_result = subprocess.run([
                self.adb_path, '-s', self.current_device, 'shell',
                'getprop', 'ro.build.version.release'
            ], capture_output=True, text=True, check=True)

            # Get screen resolution
            resolution_result = subprocess.run([
                self.adb_path, '-s', self.current_device, 'shell',
                'wm', 'size'
            ], capture_output=True, text=True, check=True)
            
            return {
                'device_id': self.current_device,
                'model': model_result.stdout.strip(),
                'android_version': version_result.stdout.strip(),
                'resolution': resolution_result.stdout.strip()
            }
            
        except subprocess.CalledProcessError as e:
            print(f"Error getting device info: {e}")
            return {'device_id': self.current_device}

    def get_installed_apps(self) -> List[dict]:
        """Lấy danh sách ứng dụng đã cài đặt trên thiết bị"""
        if not self.current_device:
            return []

        try:
            # Lấy danh sách package
            result = subprocess.run([
                self.adb_path, '-s', self.current_device, 'shell',
                'pm', 'list', 'packages', '-3'  # -3 để chỉ lấy app của bên thứ 3
            ], capture_output=True, text=True, check=True, timeout=30)

            apps = []
            for line in result.stdout.strip().split('\n'):
                if line.startswith('package:'):
                    package_name = line.replace('package:', '').strip()

                    # Lấy tên hiển thị thực tế của ứng dụng
                    app_name = self.get_app_display_name(package_name)

                    apps.append({
                        'package_name': package_name,
                        'app_name': app_name
                    })

            return apps

        except subprocess.CalledProcessError as e:
            print(f"Lỗi khi lấy danh sách ứng dụng: {e}")
            return []
        except subprocess.TimeoutExpired:
            print("Timeout khi lấy danh sách ứng dụng")
            return []

    def get_app_display_name(self, package_name: str) -> str:
        """Lấy tên hiển thị thực tế của ứng dụng"""
        try:
            # Phương pháp 1: Sử dụng pm dump để lấy applicationLabel
            result = subprocess.run([
                self.adb_path, '-s', self.current_device, 'shell',
                f'pm dump {package_name}'
            ], capture_output=True, text=True, timeout=10)

            if result.returncode == 0 and result.stdout:
                for line in result.stdout.split('\n'):
                    # Tìm applicationLabel
                    if 'applicationLabel=' in line:
                        label = line.split('applicationLabel=')[1].strip()
                        if label and label != package_name and not label.startswith('0x'):
                            return label

                    # Tìm android:label trong manifest
                    if 'android:label=' in line:
                        label = line.split('android:label=')[1].strip().strip('"')
                        if label and not label.startswith('@') and label != package_name:
                            return label

            # Phương pháp 2: Sử dụng aapt nếu có
            try:
                result = subprocess.run([
                    self.adb_path, '-s', self.current_device, 'shell',
                    f'aapt dump badging $(pm path {package_name} | cut -d: -f2) 2>/dev/null | grep "application-label:"'
                ], capture_output=True, text=True, timeout=10)

                if result.returncode == 0 and result.stdout:
                    for line in result.stdout.split('\n'):
                        if 'application-label:' in line:
                            # Parse application-label:'App Name'
                            start = line.find("'") + 1
                            end = line.rfind("'")
                            if start > 0 and end > start:
                                return line[start:end]
            except:
                pass

        except Exception as e:
            print(f"Lỗi khi lấy tên app {package_name}: {e}")

        # Fallback: Tạo tên đẹp từ package name
        return self.beautify_package_name(package_name)

    def beautify_package_name(self, package_name: str) -> str:
        """Tạo tên đẹp từ package name"""
        try:
            # Lấy phần cuối của package name
            parts = package_name.split('.')
            if len(parts) > 1:
                # Lấy 2 phần cuối và loại bỏ các từ thường gặp
                name_parts = []
                for part in parts[-2:]:
                    if part.lower() not in ['com', 'app', 'android', 'mobile', 'client']:
                        name_parts.append(part)

                if name_parts:
                    name = ' '.join(name_parts)
                else:
                    name = parts[-1]  # Lấy phần cuối cùng
            else:
                name = package_name

            # Capitalize first letter of each word
            name = ' '.join(word.capitalize() for word in name.split())

            return name if name else package_name

        except Exception:
            return package_name

    def detect_ldplayer_adb(self):
        """Tự động phát hiện đường dẫn ADB của LDPlayer"""
        possible_paths = []

        # Các đường dẫn thường gặp của LDPlayer
        ldplayer_paths = [
            r"C:\LDPlayer\LDPlayer4.0",
            r"C:\LDPlayer\LDPlayer9",
            r"C:\LDPlayer\LDPlayer64",
            r"D:\LDPlayer\LDPlayer4.0",
            r"D:\LDPlayer\LDPlayer9",
            r"D:\LDPlayer\LDPlayer64",
            r"E:\LDPlayer\LDPlayer4.0",
            r"E:\LDPlayer\LDPlayer9",
            r"E:\LDPlayer\LDPlayer64"
        ]

        for path in ldplayer_paths:
            adb_path = os.path.join(path, "adb.exe")
            if os.path.exists(adb_path):
                possible_paths.append(adb_path)

        return possible_paths

    def connect_ldplayer(self, ldplayer_instance=0):
        """Kết nối với LDPlayer instance cụ thể"""
        try:
            # LDPlayer thường sử dụng port 5555 + instance number
            port = 5555 + ldplayer_instance
            device_address = f"127.0.0.1:{port}"

            # Thử kết nối
            result = subprocess.run([
                self.adb_path, "connect", device_address
            ], capture_output=True, text=True)

            if "connected" in result.stdout.lower():
                return device_address
            else:
                raise Exception(f"Không thể kết nối với LDPlayer instance {ldplayer_instance}")

        except Exception as e:
            raise Exception(f"Lỗi kết nối LDPlayer: {e}")

    def get_ldplayer_instances(self):
        """Lấy danh sách các LDPlayer instance đang chạy"""
        instances = []

        # Thử kết nối với các port thường dùng của LDPlayer
        for i in range(10):  # Thử từ 0 đến 9
            port = 5555 + i
            device_address = f"127.0.0.1:{port}"

            try:
                # Kiểm tra xem port có đang được sử dụng không
                result = subprocess.run([
                    self.adb_path, "connect", device_address
                ], capture_output=True, text=True, timeout=5)

                if "connected" in result.stdout.lower() or "already connected" in result.stdout.lower():
                    instances.append({
                        'instance': i,
                        'address': device_address,
                        'name': f"LDPlayer-{i}"
                    })
            except:
                continue

        return instances
