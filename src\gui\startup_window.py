"""
Startup Window - <PERSON><PERSON><PERSON> <PERSON>ện khởi động và thiết lập ADB
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import threading
import os
import subprocess
from ..adb_manager import ADBManager
from ..database.config_manager import ConfigManager


class StartupWindow:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🤖 Phần mềm quản lý tự động phiên bản - Khởi động")
        self.root.geometry("1200x700")
        self.root.resizable(True, True)

        # Initialize managers
        self.adb_manager = ADBManager()
        self.config_manager = ConfigManager()
        self.installed_apps = []
        self.selected_app = None
        self.current_emulator = None

        # Variables
        self.adb_path_var = tk.StringVar()

        self.setup_ui()
        
    def setup_ui(self):
        """Setup the main UI with 3 panels"""
        # Configure root grid
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)

        # Main container
        main_frame = ttk.Frame(self.root, padding="5")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        main_frame.columnconfigure(0, weight=0)  # Left panel fixed width
        main_frame.columnconfigure(1, weight=1)  # Middle panel expands
        main_frame.columnconfigure(2, weight=2)  # Right panel expands more
        main_frame.rowconfigure(0, weight=1)

        # Left Panel - Emulator Manager
        self.setup_emulator_panel(main_frame)

        # Middle Panel - ADB Setup & Device Management
        self.setup_adb_panel(main_frame)

        # Right Panel - App List & Task Management
        self.setup_app_panel(main_frame)

        # Status bar at bottom
        self.setup_status_bar(main_frame)

    def setup_emulator_panel(self, parent):
        """Setup emulator management panel"""
        emulator_frame = ttk.LabelFrame(parent, text="🎮 Quản lý giả lập", padding="10")
        emulator_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 5))
        emulator_frame.columnconfigure(0, weight=1)
        emulator_frame.rowconfigure(1, weight=1)

        # Emulator controls
        controls_frame = ttk.Frame(emulator_frame)
        controls_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        controls_frame.columnconfigure(0, weight=1)

        # Auto detect button
        ttk.Button(controls_frame, text="🔍 Tự động phát hiện LDPlayer",
                  command=self.auto_detect_emulators).grid(row=0, column=0, sticky=(tk.W, tk.E), pady=2)

        # Add emulator button
        ttk.Button(controls_frame, text="➕ Thêm giả lập",
                  command=self.add_emulator_manual).grid(row=1, column=0, sticky=(tk.W, tk.E), pady=2)

        # Start/Stop emulator buttons
        ttk.Button(controls_frame, text="▶️ Khởi động",
                  command=self.start_emulator).grid(row=2, column=0, sticky=(tk.W, tk.E), pady=2)

        ttk.Button(controls_frame, text="⏹️ Dừng",
                  command=self.stop_emulator).grid(row=3, column=0, sticky=(tk.W, tk.E), pady=2)

        # Refresh button
        ttk.Button(controls_frame, text="🔄 Làm mới",
                  command=self.refresh_emulators).grid(row=4, column=0, sticky=(tk.W, tk.E), pady=2)

        # Test button for debugging
        ttk.Button(controls_frame, text="🧪 Test ADB",
                  command=self.test_adb_connection).grid(row=5, column=0, sticky=(tk.W, tk.E), pady=2)

        # Emulator list
        list_frame = ttk.Frame(emulator_frame)
        list_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        list_frame.columnconfigure(0, weight=1)
        list_frame.rowconfigure(0, weight=1)

        # Listbox with scrollbar
        self.emulator_listbox = tk.Listbox(list_frame, height=10)
        self.emulator_listbox.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        emulator_scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.emulator_listbox.yview)
        emulator_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.emulator_listbox.config(yscrollcommand=emulator_scrollbar.set)

        # Bind events
        self.emulator_listbox.bind('<<ListboxSelect>>', self.on_emulator_select)
        self.emulator_listbox.bind('<Double-Button-1>', self.on_emulator_double_click)

        # Context menu
        self.emulator_context_menu = tk.Menu(self.root, tearoff=0)
        self.emulator_context_menu.add_command(label="🔌 Kết nối", command=self.connect_emulator)
        self.emulator_context_menu.add_command(label="❌ Ngắt kết nối", command=self.disconnect_emulator)
        self.emulator_context_menu.add_separator()
        self.emulator_context_menu.add_command(label="✏️ Sửa", command=self.edit_emulator)
        self.emulator_context_menu.add_command(label="🗑️ Xóa", command=self.delete_emulator)

        self.emulator_listbox.bind('<Button-3>', self.show_emulator_context_menu)

        # Load emulators
        self.refresh_emulators()

    def setup_adb_panel(self, parent):
        """Setup ADB management panel"""
        adb_frame = ttk.LabelFrame(parent, text="⚙️ Thiết lập ADB & Thiết bị", padding="10")
        adb_frame.grid(row=0, column=1, sticky=(tk.W, tk.E, tk.N, tk.S), padx=5)
        adb_frame.columnconfigure(0, weight=1)
        adb_frame.rowconfigure(3, weight=1)

        # ADB Path input
        path_frame = ttk.Frame(adb_frame)
        path_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        path_frame.columnconfigure(0, weight=1)

        ttk.Label(path_frame, text="Đường dẫn ADB:").grid(row=0, column=0, sticky=tk.W)
        self.adb_path_entry = ttk.Entry(path_frame, textvariable=self.adb_path_var, width=30)
        self.adb_path_entry.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(5, 0))

        # Buttons
        btn_frame = ttk.Frame(adb_frame)
        btn_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(10, 10))
        btn_frame.columnconfigure(0, weight=1)

        ttk.Button(btn_frame, text="📁 Chọn file ADB",
                  command=self.browse_adb_path).grid(row=0, column=0, sticky=(tk.W, tk.E), pady=2)
        ttk.Button(btn_frame, text="🔌 Kết nối ADB",
                  command=self.connect_adb).grid(row=1, column=0, sticky=(tk.W, tk.E), pady=2)
        ttk.Button(btn_frame, text="📱 Lấy danh sách app",
                  command=self.get_app_list).grid(row=2, column=0, sticky=(tk.W, tk.E), pady=2)

        # Device list
        device_frame = ttk.LabelFrame(adb_frame, text="Thiết bị kết nối", padding="5")
        device_frame.grid(row=2, column=0, sticky=(tk.W, tk.E), pady=(10, 10))
        device_frame.columnconfigure(0, weight=1)
        device_frame.rowconfigure(0, weight=1)

        # Device listbox with scrollbar
        device_container = ttk.Frame(device_frame)
        device_container.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        device_container.columnconfigure(0, weight=1)
        device_container.rowconfigure(0, weight=1)

        self.device_listbox = tk.Listbox(device_container, height=4)
        self.device_listbox.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        device_scrollbar = ttk.Scrollbar(device_container, orient=tk.VERTICAL, command=self.device_listbox.yview)
        device_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.device_listbox.config(yscrollcommand=device_scrollbar.set)

        self.device_listbox.bind('<<ListboxSelect>>', self.on_device_select)

        # Connection status
        self.connection_status_label = ttk.Label(device_frame, text="Chưa kết nối", foreground="red")
        self.connection_status_label.grid(row=1, column=0, pady=(5, 0))
        
        # Device list area
        device_label = ttk.Label(adb_frame, text="Thiết bị kết nối:")
        device_label.grid(row=2, column=0, sticky=(tk.W, tk.N), pady=(10, 5))
        
        # Device listbox with scrollbar
        device_list_frame = ttk.Frame(adb_frame)
        device_list_frame.grid(row=3, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        device_list_frame.columnconfigure(0, weight=1)
        device_list_frame.rowconfigure(0, weight=1)
        
        self.device_listbox = tk.Listbox(device_list_frame, height=8)
        self.device_listbox.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        device_scrollbar = ttk.Scrollbar(device_list_frame, orient="vertical", command=self.device_listbox.yview)
        device_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.device_listbox.configure(yscrollcommand=device_scrollbar.set)
        
        # Bind device selection
        self.device_listbox.bind('<<ListboxSelect>>', self.on_device_select)

    def setup_app_panel(self, parent):
        """Setup app list and task management panel"""
        app_frame = ttk.LabelFrame(parent, text="📱 Ứng dụng & Tác vụ", padding="10")
        app_frame.grid(row=0, column=2, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(5, 0))
        app_frame.columnconfigure(0, weight=1)
        app_frame.rowconfigure(1, weight=1)
        app_frame.rowconfigure(3, weight=1)

        # App list section
        app_list_frame = ttk.LabelFrame(app_frame, text="Danh sách ứng dụng", padding="5")
        app_list_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        app_list_frame.columnconfigure(0, weight=1)
        app_list_frame.rowconfigure(0, weight=1)

        # App listbox with scrollbar
        app_list_container = ttk.Frame(app_list_frame)
        app_list_container.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        app_list_container.columnconfigure(0, weight=1)
        app_list_container.rowconfigure(0, weight=1)

        self.app_listbox = tk.Listbox(app_list_container, height=8)
        self.app_listbox.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        app_scrollbar = ttk.Scrollbar(app_list_container, orient=tk.VERTICAL, command=self.app_listbox.yview)
        app_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.app_listbox.config(yscrollcommand=app_scrollbar.set)

        # Bind events
        self.app_listbox.bind('<Double-Button-1>', self.on_app_double_click)

        # Task management section
        task_frame = ttk.LabelFrame(app_frame, text="Quản lý tác vụ", padding="5")
        task_frame.grid(row=2, column=0, sticky=(tk.W, tk.E), pady=(10, 10))
        task_frame.columnconfigure(0, weight=1)

        # Task controls
        task_controls = ttk.Frame(task_frame)
        task_controls.grid(row=0, column=0, sticky=(tk.W, tk.E))
        task_controls.columnconfigure(0, weight=1)
        task_controls.columnconfigure(1, weight=1)

        ttk.Button(task_controls, text="🔄 Làm mới tác vụ",
                  command=self.refresh_tasks).grid(row=0, column=0, sticky=(tk.W, tk.E), padx=(0, 2))
        ttk.Button(task_controls, text="🗑️ Xóa tác vụ",
                  command=self.delete_selected_task).grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(2, 0))

        # Task list
        task_list_frame = ttk.Frame(app_frame)
        task_list_frame.grid(row=3, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        task_list_frame.columnconfigure(0, weight=1)
        task_list_frame.rowconfigure(0, weight=1)

        self.task_listbox = tk.Listbox(task_list_frame, height=8)
        self.task_listbox.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        task_scrollbar = ttk.Scrollbar(task_list_frame, orient=tk.VERTICAL, command=self.task_listbox.yview)
        task_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.task_listbox.config(yscrollcommand=task_scrollbar.set)

        # Load initial tasks
        self.refresh_tasks()


    def setup_status_bar(self, parent):
        """Setup status bar"""
        status_frame = ttk.Frame(parent)
        status_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(10, 0))
        status_frame.columnconfigure(0, weight=1)

        self.status_label = ttk.Label(status_frame, text="Sẵn sàng - Vui lòng chọn giả lập hoặc thiết lập ADB")
        self.status_label.grid(row=0, column=0, sticky=tk.W)
        
    def browse_adb_path(self):
        """Browse for ADB executable path"""
        try:
            file_path = filedialog.askopenfilename(
                title="Chọn file ADB executable",
                filetypes=[("Executable files", "*.exe"), ("All files", "*.*")]
            )
            if file_path:
                # Validate ADB file exists
                if not os.path.exists(file_path):
                    messagebox.showerror("Lỗi", f"File không tồn tại: {file_path}")
                    return

                # Validate it's actually adb.exe
                if not file_path.lower().endswith('adb.exe'):
                    result = messagebox.askyesno("Xác nhận",
                                               f"File được chọn không phải adb.exe: {os.path.basename(file_path)}\n"
                                               "Bạn có muốn tiếp tục không?")
                    if not result:
                        return

                self.adb_path_var.set(file_path)
                self.adb_manager.set_adb_path(file_path)

                # Test ADB first
                if not self.adb_manager.test_adb_connection():
                    messagebox.showerror("Lỗi ADB",
                                       f"File ADB không hợp lệ hoặc không thể chạy:\n{file_path}\n\n"
                                       "Vui lòng kiểm tra:\n"
                                       "1. File có phải adb.exe không\n"
                                       "2. File có bị hỏng không\n"
                                       "3. Có quyền thực thi không")
                    return

                self.status_label.config(text=f"Đã thiết lập ADB: {os.path.basename(file_path)}")

                # Test device connection
                try:
                    self.refresh_devices()
                    messagebox.showinfo("Thành công",
                                      f"Đã thiết lập ADB thành công!\n"
                                      f"File: {os.path.basename(file_path)}\n"
                                      f"Đường dẫn: {file_path}")
                except Exception as e:
                    messagebox.showwarning("Cảnh báo",
                                         f"ADB đã được thiết lập nhưng không tìm thấy thiết bị:\n{str(e)}\n\n"
                                         "Vui lòng kiểm tra:\n"
                                         "1. LDPlayer đã khởi động chưa\n"
                                         "2. USB Debugging đã bật chưa\n"
                                         "3. Thiết bị đã kết nối chưa")

        except Exception as e:
            messagebox.showerror("Lỗi", f"Lỗi khi thiết lập ADB: {str(e)}")


            
    def refresh_devices(self):
        """Refresh device list"""
        self.device_listbox.delete(0, tk.END)
        
        try:
            devices = self.adb_manager.get_connected_devices()
            
            if devices:
                for device in devices:
                    self.device_listbox.insert(tk.END, device)
                self.status_label.config(text=f"Tìm thấy {len(devices)} thiết bị")
            else:
                self.device_listbox.insert(tk.END, "Không tìm thấy thiết bị")
                self.status_label.config(text="Không có thiết bị kết nối")
        except Exception as e:
            self.device_listbox.insert(tk.END, "Lỗi kết nối ADB")
            self.status_label.config(text=f"Lỗi ADB: {str(e)[:50]}...")
            
    def on_device_select(self, event):
        """Handle device selection"""
        selection = self.device_listbox.curselection()
        if selection:
            device_id = self.device_listbox.get(selection[0])
            if device_id != "Không tìm thấy thiết bị" and device_id != "Lỗi kết nối ADB":
                self.adb_manager.select_device(device_id)
                self.status_label.config(text=f"Đã chọn thiết bị: {device_id}")
                self.connection_status_label.config(
                    text=f"Đã chọn: {device_id}",
                    foreground="blue"
                )

                # Refresh app list for selected device
                self.get_app_list()

    def get_app_list(self):
        """Get list of installed apps"""
        print(f"DEBUG: get_app_list called")
        print(f"DEBUG: current_device = {self.adb_manager.current_device}")
        print(f"DEBUG: adb_path = {self.adb_manager.adb_path}")

        if not self.adb_manager.current_device:
            messagebox.showwarning("Cảnh báo", "Vui lòng chọn thiết bị trước")
            return

        # Update ADB path if changed
        adb_path = self.adb_path_var.get().strip()
        if adb_path:
            self.adb_manager.set_adb_path(adb_path)

        self.app_listbox.delete(0, tk.END)
        self.app_listbox.insert(tk.END, "Đang tải danh sách ứng dụng...")
        self.status_label.config(text="Đang lấy danh sách ứng dụng...")

        # Get apps in background thread
        def get_apps():
            try:
                print(f"DEBUG: Getting apps for device {self.adb_manager.current_device}")
                apps = self.adb_manager.get_installed_apps()
                print(f"DEBUG: Found {len(apps)} apps")
                self.installed_apps = apps

                # Update UI in main thread
                self.root.after(0, self.update_app_listbox, apps)
            except Exception as e:
                print(f"DEBUG: Error getting apps: {e}")
                self.root.after(0, lambda: messagebox.showerror("Lỗi", f"Không thể lấy danh sách ứng dụng: {e}"))
                self.root.after(0, lambda: self.status_label.config(text="Lỗi khi lấy danh sách ứng dụng"))

        threading.Thread(target=get_apps, daemon=True).start()
        
    def update_app_listbox(self, apps):
        """Update app listbox with app list"""
        self.app_listbox.delete(0, tk.END)
        if apps:
            for app in apps:
                display_text = f"{app['app_name']} ({app['package_name']})"
                self.app_listbox.insert(tk.END, display_text)
            self.status_label.config(text=f"Tìm thấy {len(apps)} ứng dụng")
        else:
            self.app_listbox.insert(tk.END, "Không tìm thấy ứng dụng nào")
            self.status_label.config(text="Không có ứng dụng")
            
    def on_app_double_click(self, event):
        """Handle app double click to open config window"""
        selection = self.app_listbox.curselection()
        if selection:
            index = selection[0]
            if index < len(self.installed_apps):
                app = self.installed_apps[index]
                self.open_config_window(app)
                
    def open_config_window(self, app):
        """Open configuration window for selected app"""
        from .config_window import ConfigWindow
        config_window = ConfigWindow(app, self.adb_manager)
        
    # Emulator Management Methods
    def auto_detect_emulators(self):
        """Auto detect LDPlayer emulators"""
        try:
            ldplayer_paths = self.adb_manager.detect_ldplayer_adb()

            if not ldplayer_paths:
                messagebox.showinfo("Thông báo",
                                  "Không tìm thấy LDPlayer instance nào đang chạy.\n"
                                  "Vui lòng khởi động LDPlayer trước.")
                return

            # Save detected emulators to database
            for i, adb_path in enumerate(ldplayer_paths):
                emulator_name = f"LDPlayer {i+1}"
                emulator_config = {
                    'name': emulator_name,
                    'adb_path': adb_path,
                    'port': 5555 + i,
                    'status': 'detected'
                }
                self.config_manager.save_emulator(emulator_config)

            self.refresh_emulators()
            messagebox.showinfo("Thành công", f"Đã phát hiện {len(ldplayer_paths)} LDPlayer instances!")

        except Exception as e:
            messagebox.showerror("Lỗi", f"Lỗi khi phát hiện LDPlayer: {e}")

    def add_emulator_manual(self):
        """Add emulator manually"""
        # Simple dialog for manual emulator addition
        dialog = tk.Toplevel(self.root)
        dialog.title("Thêm giả lập")
        dialog.geometry("400x300")
        dialog.transient(self.root)
        dialog.grab_set()

        # Center dialog
        dialog.geometry("+%d+%d" % (self.root.winfo_rootx() + 50, self.root.winfo_rooty() + 50))

        # Form fields
        ttk.Label(dialog, text="Tên giả lập:").grid(row=0, column=0, sticky=tk.W, padx=10, pady=5)
        name_var = tk.StringVar()
        ttk.Entry(dialog, textvariable=name_var, width=30).grid(row=0, column=1, padx=10, pady=5)

        ttk.Label(dialog, text="Đường dẫn ADB:").grid(row=1, column=0, sticky=tk.W, padx=10, pady=5)
        path_var = tk.StringVar()
        path_entry = ttk.Entry(dialog, textvariable=path_var, width=30)
        path_entry.grid(row=1, column=1, padx=10, pady=5)

        def browse_path():
            file_path = filedialog.askopenfilename(
                title="Chọn file ADB executable",
                filetypes=[("Executable files", "*.exe"), ("All files", "*.*")]
            )
            if file_path:
                path_var.set(file_path)

        ttk.Button(dialog, text="Browse", command=browse_path).grid(row=1, column=2, padx=5, pady=5)

        ttk.Label(dialog, text="Port:").grid(row=2, column=0, sticky=tk.W, padx=10, pady=5)
        port_var = tk.StringVar(value="5555")
        ttk.Entry(dialog, textvariable=port_var, width=30).grid(row=2, column=1, padx=10, pady=5)

        def save_emulator():
            if not name_var.get() or not path_var.get():
                messagebox.showerror("Lỗi", "Vui lòng điền đầy đủ thông tin")
                return

            try:
                emulator_config = {
                    'name': name_var.get(),
                    'adb_path': path_var.get(),
                    'port': int(port_var.get()),
                    'status': 'manual'
                }
                self.config_manager.save_emulator(emulator_config)
                self.refresh_emulators()
                dialog.destroy()
                messagebox.showinfo("Thành công", "Đã thêm giả lập thành công!")
            except Exception as e:
                messagebox.showerror("Lỗi", f"Lỗi khi thêm giả lập: {e}")

        ttk.Button(dialog, text="Lưu", command=save_emulator).grid(row=3, column=1, pady=20)

    def refresh_emulators(self):
        """Refresh emulator list"""
        self.emulator_listbox.delete(0, tk.END)

        try:
            emulators = self.config_manager.get_emulators()
            for emulator in emulators:
                status_icon = "🟢" if emulator['status'] == 'connected' else "🔴"
                display_text = f"{status_icon} {emulator['name']} ({emulator['status']})"
                self.emulator_listbox.insert(tk.END, display_text)
        except Exception as e:
            self.emulator_listbox.insert(tk.END, f"Lỗi: {e}")

    def on_emulator_select(self, event):
        """Handle emulator selection"""
        selection = self.emulator_listbox.curselection()
        if selection:
            try:
                emulators = self.config_manager.get_emulators()
                if selection[0] < len(emulators):
                    self.current_emulator = emulators[selection[0]]
                    # Update ADB path from selected emulator
                    self.adb_path_var.set(self.current_emulator['adb_path'])
            except Exception as e:
                print(f"Error selecting emulator: {e}")

    def on_emulator_double_click(self, event):
        """Handle emulator double click to connect"""
        self.connect_emulator()

    def show_emulator_context_menu(self, event):
        """Show context menu for emulator"""
        try:
            self.emulator_context_menu.tk_popup(event.x_root, event.y_root)
        finally:
            self.emulator_context_menu.grab_release()

    def connect_emulator(self):
        """Connect to selected emulator"""
        if not self.current_emulator:
            messagebox.showwarning("Cảnh báo", "Vui lòng chọn một giả lập")
            return

        try:
            # Set ADB path and connect
            adb_path = self.current_emulator['adb_path']
            self.adb_manager.set_adb_path(adb_path)
            self.adb_path_var.set(adb_path)

            # Test ADB connection first
            if not self.adb_manager.test_adb_connection():
                messagebox.showerror("Lỗi", "Không thể kết nối với ADB. Vui lòng kiểm tra đường dẫn ADB.")
                return

            # Try to connect to emulator port
            port = self.current_emulator.get('port', 5555)
            device_id = f"127.0.0.1:{port}"

            # Connect to emulator
            result = subprocess.run([
                adb_path, 'connect', device_id
            ], capture_output=True, text=True, timeout=15)

            print(f"ADB connect result: {result.returncode}")
            print(f"ADB connect stdout: {result.stdout}")
            print(f"ADB connect stderr: {result.stderr}")

            if result.returncode == 0 or "connected" in result.stdout.lower():
                # Update emulator status
                self.config_manager.update_emulator_status(
                    self.current_emulator['id'], 'connected', device_id
                )

                # Update UI and refresh devices
                self.refresh_emulators()

                # Wait a moment then refresh devices and auto-select
                self.root.after(2000, lambda: self.post_connect_setup(device_id))

                self.connection_status_label.config(
                    text=f"Đã kết nối: {self.current_emulator['name']} ({device_id})",
                    foreground="green"
                )

                messagebox.showinfo("Thành công", f"Đã kết nối với {self.current_emulator['name']}")
            else:
                messagebox.showerror("Lỗi", f"Không thể kết nối với emulator:\n{result.stderr}")

        except Exception as e:
            messagebox.showerror("Lỗi", f"Lỗi khi kết nối: {e}")

    def post_connect_setup(self, device_id):
        """Setup after successful connection"""
        try:
            # Refresh devices list
            self.refresh_devices()

            # Auto select device after a short delay
            self.root.after(1000, lambda: self.auto_select_device(device_id))
        except Exception as e:
            print(f"Post connect setup failed: {e}")

    def start_emulator(self):
        """Start selected emulator"""
        if not self.current_emulator:
            messagebox.showwarning("Cảnh báo", "Vui lòng chọn một giả lập")
            return

        try:
            # Extract emulator name/index from the emulator config
            emulator_name = self.current_emulator['name']

            # Try to find LDPlayer console executable
            adb_path = self.current_emulator['adb_path']
            ldplayer_dir = os.path.dirname(adb_path)

            # Look for LDConsole.exe in the same directory
            console_path = os.path.join(ldplayer_dir, "ldconsole.exe")
            if not os.path.exists(console_path):
                # Try parent directory
                console_path = os.path.join(os.path.dirname(ldplayer_dir), "ldconsole.exe")

            if os.path.exists(console_path):
                # Extract instance index from emulator name (e.g., "LDPlayer 1" -> index 0)
                import re
                match = re.search(r'(\d+)', emulator_name)
                if match:
                    instance_index = int(match.group(1)) - 1  # Convert to 0-based index
                else:
                    instance_index = 0

                # Start emulator using ldconsole
                subprocess.Popen([console_path, "launch", "--index", str(instance_index)])

                # Update status
                self.config_manager.update_emulator_status(
                    self.current_emulator['id'], 'starting'
                )
                self.refresh_emulators()

                messagebox.showinfo("Thông báo", f"Đang khởi động {emulator_name}...")

                # Auto-connect after a delay
                self.root.after(5000, self.auto_connect_after_start)

            else:
                messagebox.showerror("Lỗi", "Không tìm thấy LDConsole.exe để khởi động emulator")

        except Exception as e:
            messagebox.showerror("Lỗi", f"Lỗi khi khởi động emulator: {e}")

    def auto_connect_after_start(self):
        """Auto connect to emulator after starting"""
        try:
            self.connect_emulator()
        except Exception as e:
            print(f"Auto connect failed: {e}")

    def auto_select_device(self, device_id):
        """Auto select device and load apps"""
        try:
            print(f"DEBUG: auto_select_device called with {device_id}")
            print(f"DEBUG: device_listbox size: {self.device_listbox.size()}")

            # Find and select the device in listbox
            found = False
            for i in range(self.device_listbox.size()):
                item = self.device_listbox.get(i)
                print(f"DEBUG: checking item {i}: {item}")
                if device_id in item:
                    print(f"DEBUG: Found device at index {i}")
                    # Select the item in listbox
                    self.device_listbox.selection_clear(0, tk.END)
                    self.device_listbox.selection_set(i)
                    self.device_listbox.activate(i)

                    # Select device in ADB manager
                    self.adb_manager.select_device(device_id)
                    print(f"DEBUG: Selected device in ADB manager: {self.adb_manager.current_device}")

                    # Update status
                    self.status_label.config(text=f"Đã chọn thiết bị: {device_id}")
                    self.connection_status_label.config(
                        text=f"Đã chọn: {device_id}",
                        foreground="blue"
                    )

                    # Load app list
                    self.get_app_list()
                    found = True
                    break

            if not found:
                print(f"DEBUG: Device {device_id} not found in listbox")

        except Exception as e:
            print(f"Auto select device failed: {e}")

    def stop_emulator(self):
        """Stop selected emulator"""
        if not self.current_emulator:
            messagebox.showwarning("Cảnh báo", "Vui lòng chọn một giả lập")
            return

        try:
            # Extract emulator name/index from the emulator config
            emulator_name = self.current_emulator['name']

            # Try to find LDPlayer console executable
            adb_path = self.current_emulator['adb_path']
            ldplayer_dir = os.path.dirname(adb_path)

            # Look for LDConsole.exe in the same directory
            console_path = os.path.join(ldplayer_dir, "ldconsole.exe")
            if not os.path.exists(console_path):
                # Try parent directory
                console_path = os.path.join(os.path.dirname(ldplayer_dir), "ldconsole.exe")

            if os.path.exists(console_path):
                # Extract instance index from emulator name
                import re
                match = re.search(r'(\d+)', emulator_name)
                if match:
                    instance_index = int(match.group(1)) - 1  # Convert to 0-based index
                else:
                    instance_index = 0

                # Stop emulator using ldconsole
                subprocess.run([console_path, "quit", "--index", str(instance_index)])

                # Update status
                self.config_manager.update_emulator_status(
                    self.current_emulator['id'], 'stopped'
                )
                self.refresh_emulators()
                self.connection_status_label.config(text="Chưa kết nối", foreground="red")

                messagebox.showinfo("Thành công", f"Đã dừng {emulator_name}")

            else:
                messagebox.showerror("Lỗi", "Không tìm thấy LDConsole.exe để dừng emulator")

        except Exception as e:
            messagebox.showerror("Lỗi", f"Lỗi khi dừng emulator: {e}")

    def disconnect_emulator(self):
        """Disconnect from selected emulator"""
        if not self.current_emulator:
            messagebox.showwarning("Cảnh báo", "Vui lòng chọn một giả lập")
            return

        try:
            self.config_manager.update_emulator_status(
                self.current_emulator['id'], 'disconnected'
            )
            self.refresh_emulators()
            messagebox.showinfo("Thành công", f"Đã ngắt kết nối {self.current_emulator['name']}")
        except Exception as e:
            messagebox.showerror("Lỗi", f"Lỗi khi ngắt kết nối: {e}")

    def edit_emulator(self):
        """Edit selected emulator"""
        # TODO: Implement emulator editing
        messagebox.showinfo("Thông báo", "Tính năng sửa giả lập sẽ được thêm sau")

    def delete_emulator(self):
        """Delete selected emulator"""
        if not self.current_emulator:
            messagebox.showwarning("Cảnh báo", "Vui lòng chọn một giả lập")
            return

        result = messagebox.askyesno("Xác nhận",
                                   f"Bạn có chắc muốn xóa giả lập '{self.current_emulator['name']}'?")
        if result:
            # TODO: Implement emulator deletion
            messagebox.showinfo("Thông báo", "Tính năng xóa giả lập sẽ được thêm sau")

    def connect_adb(self):
        """Connect to ADB with current path"""
        if not self.adb_path_var.get():
            messagebox.showwarning("Cảnh báo", "Vui lòng chọn đường dẫn ADB trước")
            return

        try:
            self.adb_manager.set_adb_path(self.adb_path_var.get())
            if self.adb_manager.test_adb_connection():
                self.refresh_devices()
                messagebox.showinfo("Thành công", "Đã kết nối ADB thành công!")
            else:
                messagebox.showerror("Lỗi", "Không thể kết nối với ADB")
        except Exception as e:
            messagebox.showerror("Lỗi", f"Lỗi khi kết nối ADB: {e}")

    def test_adb_connection(self):
        """Test ADB connection for debugging"""
        if not self.adb_path_var.get():
            messagebox.showwarning("Cảnh báo", "Vui lòng chọn đường dẫn ADB trước")
            return

        try:
            self.adb_manager.set_adb_path(self.adb_path_var.get())
            if self.adb_manager.test_adb_connection():
                messagebox.showinfo("Test ADB", "✅ ADB connection thành công!")
            else:
                messagebox.showerror("Test ADB", "❌ ADB connection thất bại!")
        except Exception as e:
            messagebox.showerror("Test ADB", f"❌ Lỗi khi test ADB: {e}")

    # Task Management Methods
    def refresh_tasks(self):
        """Refresh task list"""
        self.task_listbox.delete(0, tk.END)

        try:
            # Get tasks for current emulator
            emulator_name = self.current_emulator['name'] if self.current_emulator else None
            tasks = self.config_manager.get_tasks(emulator_name=emulator_name)

            for task in tasks:
                task_type = task.get('task_type', 'click')
                if task_type == "click":
                    icon = "🖱️"
                    detail = f"({task['click_type']})"
                elif task_type == "check_time":
                    icon = "⏰"
                    detail = "(kiểm tra thời gian)"
                elif task_type == "wait":
                    icon = "⏳"
                    detail = f"(chờ {task.get('wait_time', '0')}s)"
                else:
                    icon = "📋"
                    detail = f"({task_type})"

                display_text = f"{icon} {task['task_name']} - {task['app_name']} {detail}"
                self.task_listbox.insert(tk.END, display_text)

            if not tasks:
                self.task_listbox.insert(tk.END, "Chưa có tác vụ nào")

        except Exception as e:
            self.task_listbox.insert(tk.END, f"Lỗi: {e}")

    def delete_selected_task(self):
        """Delete selected task"""
        selection = self.task_listbox.curselection()
        if not selection:
            messagebox.showwarning("Cảnh báo", "Vui lòng chọn một tác vụ để xóa")
            return

        try:
            emulator_name = self.current_emulator['name'] if self.current_emulator else None
            tasks = self.config_manager.get_tasks(emulator_name=emulator_name)

            if selection[0] < len(tasks):
                task = tasks[selection[0]]
                result = messagebox.askyesno("Xác nhận",
                                           f"Bạn có chắc muốn xóa tác vụ '{task['task_name']}'?")
                if result:
                    self.config_manager.delete_task(task['id'])
                    self.refresh_tasks()
                    messagebox.showinfo("Thành công", "Đã xóa tác vụ thành công!")
        except Exception as e:
            messagebox.showerror("Lỗi", f"Lỗi khi xóa tác vụ: {e}")

    def run(self):
        """Run the application"""
        self.root.mainloop()
