"""
Startup Window - <PERSON><PERSON><PERSON>ện khởi động và thiết lập ADB
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import threading
import os
from ..adb_manager import ADBManager


class StartupWindow:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🤖 Phần mềm quản lý tự động phiên bản - Khởi động")
        self.root.geometry("850x600")
        self.root.resizable(True, True)
        
        # Initialize managers
        self.adb_manager = ADBManager()
        self.installed_apps = []
        self.selected_app = None
        
        # Variables
        self.adb_path_var = tk.StringVar()
        
        self.setup_ui()
        
    def setup_ui(self):
        """Setup the main UI"""
        # Main container
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(1, weight=1)
        
        # Title
        title_label = ttk.Label(main_frame, text="Phần mềm quản lý tự động phiên bản", 
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, pady=(0, 20))
        
        # Content frame
        content_frame = ttk.Frame(main_frame)
        content_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        content_frame.columnconfigure(0, weight=1)
        content_frame.columnconfigure(1, weight=1)
        content_frame.rowconfigure(0, weight=1)
        
        # Left panel - ADB Management
        self.setup_adb_panel(content_frame)
        
        # Right panel - App List
        self.setup_app_list_panel(content_frame)
        
        # Status bar
        self.setup_status_bar(main_frame)
        
        # Configure root window
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        
    def setup_adb_panel(self, parent):
        """Setup ADB management panel"""
        adb_frame = ttk.LabelFrame(parent, text="Khu vực hiển thị adb", padding="10")
        adb_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 5))
        adb_frame.columnconfigure(0, weight=1)
        adb_frame.rowconfigure(2, weight=1)
        
        # ADB Path input
        path_frame = ttk.Frame(adb_frame)
        path_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        path_frame.columnconfigure(0, weight=1)
        
        ttk.Label(path_frame, text="Đường dẫn ADB:").grid(row=0, column=0, sticky=tk.W)
        self.adb_path_entry = ttk.Entry(path_frame, textvariable=self.adb_path_var, width=40)
        self.adb_path_entry.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(5, 0))
        
        # Buttons
        btn_frame = ttk.Frame(adb_frame)
        btn_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(10, 10))
        
        ttk.Button(btn_frame, text="Button thiết lập url của adb.exe hoặc giả lập",
                  command=self.browse_adb_path).grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 5))
        ttk.Button(btn_frame, text="Tự động phát hiện LDPlayer",
                  command=self.auto_detect_ldplayer).grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 5))
        ttk.Button(btn_frame, text="Button lấy danh sách các app",
                  command=self.get_app_list).grid(row=2, column=0, sticky=(tk.W, tk.E))
        
        btn_frame.columnconfigure(0, weight=1)
        
        # Device list area
        device_label = ttk.Label(adb_frame, text="Thiết bị kết nối:")
        device_label.grid(row=2, column=0, sticky=(tk.W, tk.N), pady=(10, 5))
        
        # Device listbox with scrollbar
        device_list_frame = ttk.Frame(adb_frame)
        device_list_frame.grid(row=3, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        device_list_frame.columnconfigure(0, weight=1)
        device_list_frame.rowconfigure(0, weight=1)
        
        self.device_listbox = tk.Listbox(device_list_frame, height=8)
        self.device_listbox.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        device_scrollbar = ttk.Scrollbar(device_list_frame, orient="vertical", command=self.device_listbox.yview)
        device_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.device_listbox.configure(yscrollcommand=device_scrollbar.set)
        
        # Bind device selection
        self.device_listbox.bind('<<ListboxSelect>>', self.on_device_select)
        
    def setup_app_list_panel(self, parent):
        """Setup app list panel"""
        app_frame = ttk.LabelFrame(parent, text="Hiển thị danh sách các app trong adb ở đây", padding="10")
        app_frame.grid(row=0, column=1, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(5, 0))
        app_frame.columnconfigure(0, weight=1)
        app_frame.rowconfigure(0, weight=1)
        
        # App listbox with scrollbar
        app_list_frame = ttk.Frame(app_frame)
        app_list_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        app_list_frame.columnconfigure(0, weight=1)
        app_list_frame.rowconfigure(0, weight=1)
        
        self.app_listbox = tk.Listbox(app_list_frame)
        self.app_listbox.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        app_scrollbar = ttk.Scrollbar(app_list_frame, orient="vertical", command=self.app_listbox.yview)
        app_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.app_listbox.configure(yscrollcommand=app_scrollbar.set)
        
        # Bind double click to open config window
        self.app_listbox.bind('<Double-1>', self.on_app_double_click)
        
        # Info label
        info_label = ttk.Label(app_frame, text="Khi double click sẽ hiển thị cấu hình cho app đó", 
                              font=("Arial", 9, "italic"))
        info_label.grid(row=1, column=0, pady=(10, 0))
        
    def setup_status_bar(self, parent):
        """Setup status bar"""
        status_frame = ttk.Frame(parent)
        status_frame.grid(row=2, column=0, sticky=(tk.W, tk.E), pady=(10, 0))
        status_frame.columnconfigure(0, weight=1)
        
        self.status_label = ttk.Label(status_frame, text="Sẵn sàng - Vui lòng thiết lập đường dẫn ADB")
        self.status_label.grid(row=0, column=0, sticky=tk.W)
        
    def browse_adb_path(self):
        """Browse for ADB executable path"""
        file_path = filedialog.askopenfilename(
            title="Chọn file ADB executable",
            filetypes=[("Executable files", "*.exe"), ("All files", "*.*")]
        )
        if file_path:
            self.adb_path_var.set(file_path)
            self.adb_manager.set_adb_path(file_path)
            self.status_label.config(text=f"Đã thiết lập ADB: {os.path.basename(file_path)}")
            # Auto refresh devices after setting ADB path
            self.refresh_devices()

    def auto_detect_ldplayer(self):
        """Tự động phát hiện và thiết lập LDPlayer ADB"""
        try:
            ldplayer_paths = self.adb_manager.detect_ldplayer_adb()

            if not ldplayer_paths:
                messagebox.showwarning("Không tìm thấy",
                                     "Không tìm thấy LDPlayer ADB.\n"
                                     "Vui lòng đảm bảo LDPlayer đã được cài đặt.")
                return

            if len(ldplayer_paths) == 1:
                # Chỉ có 1 đường dẫn, tự động chọn
                adb_path = ldplayer_paths[0]
                self.adb_path_var.set(adb_path)
                self.adb_manager.set_adb_path(adb_path)
                self.status_label.config(text=f"Đã phát hiện LDPlayer ADB: {os.path.basename(os.path.dirname(adb_path))}")

                # Thử kết nối với LDPlayer instances
                self.connect_ldplayer_instances()

            else:
                # Nhiều đường dẫn, cho user chọn
                choice_window = tk.Toplevel(self.root)
                choice_window.title("Chọn phiên bản LDPlayer")
                choice_window.geometry("400x300")
                choice_window.transient(self.root)
                choice_window.grab_set()

                ttk.Label(choice_window, text="Tìm thấy nhiều phiên bản LDPlayer:",
                         font=("Arial", 12, "bold")).pack(pady=10)

                listbox = tk.Listbox(choice_window)
                listbox.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

                for path in ldplayer_paths:
                    version = os.path.basename(os.path.dirname(path))
                    listbox.insert(tk.END, f"{version} - {path}")

                def select_ldplayer():
                    selection = listbox.curselection()
                    if selection:
                        selected_path = ldplayer_paths[selection[0]]
                        self.adb_path_var.set(selected_path)
                        self.adb_manager.set_adb_path(selected_path)
                        version = os.path.basename(os.path.dirname(selected_path))
                        self.status_label.config(text=f"Đã chọn {version}")
                        choice_window.destroy()
                        self.connect_ldplayer_instances()
                    else:
                        messagebox.showwarning("Cảnh báo", "Vui lòng chọn một phiên bản")

                ttk.Button(choice_window, text="Chọn", command=select_ldplayer).pack(pady=10)

        except Exception as e:
            messagebox.showerror("Lỗi", f"Lỗi khi phát hiện LDPlayer: {e}")

    def connect_ldplayer_instances(self):
        """Kết nối với các LDPlayer instance"""
        try:
            instances = self.adb_manager.get_ldplayer_instances()
            if instances:
                self.status_label.config(text=f"Tìm thấy {len(instances)} LDPlayer instance")
                self.refresh_devices()
            else:
                messagebox.showinfo("Thông báo",
                                  "Không tìm thấy LDPlayer instance nào đang chạy.\n"
                                  "Vui lòng khởi động LDPlayer trước.")
        except Exception as e:
            messagebox.showerror("Lỗi", f"Lỗi khi kết nối LDPlayer: {e}")
            
    def refresh_devices(self):
        """Refresh device list"""
        self.device_listbox.delete(0, tk.END)
        
        try:
            devices = self.adb_manager.get_connected_devices()
            
            if devices:
                for device in devices:
                    self.device_listbox.insert(tk.END, device)
                self.status_label.config(text=f"Tìm thấy {len(devices)} thiết bị")
            else:
                self.device_listbox.insert(tk.END, "Không tìm thấy thiết bị")
                self.status_label.config(text="Không có thiết bị kết nối")
        except Exception as e:
            self.device_listbox.insert(tk.END, "Lỗi kết nối ADB")
            self.status_label.config(text=f"Lỗi ADB: {str(e)[:50]}...")
            
    def on_device_select(self, event):
        """Handle device selection"""
        selection = self.device_listbox.curselection()
        if selection:
            device_id = self.device_listbox.get(selection[0])
            if device_id != "Không tìm thấy thiết bị" and device_id != "Lỗi kết nối ADB":
                self.adb_manager.select_device(device_id)
                self.status_label.config(text=f"Đã chọn thiết bị: {device_id}")
                
    def get_app_list(self):
        """Get list of installed apps"""
        if not self.adb_manager.current_device:
            messagebox.showwarning("Cảnh báo", "Vui lòng chọn thiết bị trước")
            return
            
        # Update ADB path if changed
        adb_path = self.adb_path_var.get().strip()
        if adb_path:
            self.adb_manager.set_adb_path(adb_path)
        
        self.app_listbox.delete(0, tk.END)
        self.app_listbox.insert(tk.END, "Đang tải danh sách ứng dụng...")
        self.status_label.config(text="Đang lấy danh sách ứng dụng...")
        
        # Get apps in background thread
        def get_apps():
            try:
                apps = self.adb_manager.get_installed_apps()
                self.installed_apps = apps
                
                # Update UI in main thread
                self.root.after(0, self.update_app_listbox, apps)
            except Exception as e:
                self.root.after(0, lambda: messagebox.showerror("Lỗi", f"Không thể lấy danh sách ứng dụng: {e}"))
                self.root.after(0, lambda: self.status_label.config(text="Lỗi khi lấy danh sách ứng dụng"))
        
        threading.Thread(target=get_apps, daemon=True).start()
        
    def update_app_listbox(self, apps):
        """Update app listbox with app list"""
        self.app_listbox.delete(0, tk.END)
        if apps:
            for app in apps:
                display_text = f"{app['app_name']} ({app['package_name']})"
                self.app_listbox.insert(tk.END, display_text)
            self.status_label.config(text=f"Tìm thấy {len(apps)} ứng dụng")
        else:
            self.app_listbox.insert(tk.END, "Không tìm thấy ứng dụng nào")
            self.status_label.config(text="Không có ứng dụng")
            
    def on_app_double_click(self, event):
        """Handle app double click to open config window"""
        selection = self.app_listbox.curselection()
        if selection:
            index = selection[0]
            if index < len(self.installed_apps):
                app = self.installed_apps[index]
                self.open_config_window(app)
                
    def open_config_window(self, app):
        """Open configuration window for selected app"""
        from .config_window import ConfigWindow
        config_window = ConfigWindow(app, self.adb_manager)
        
    def run(self):
        """Run the application"""
        self.root.mainloop()
