"""
Task Scheduler Module
Manages periodic execution of automation tasks
"""

import threading
import time
import logging
from typing import Dict, List, Optional
from datetime import datetime

from .adb_manager import ADBManager
from .config_manager import ConfigManager, AppTask
from .app_handler import AppHandler


class TaskScheduler:
    """Manages scheduling and execution of automation tasks"""
    
    def __init__(self, adb_manager: ADBManager, config_manager: ConfigManager):
        self.adb_manager = adb_manager
        self.config_manager = config_manager
        self.app_handler = AppHandler(adb_manager, config_manager)
        
        self.running = False
        self.scheduler_thread = None
        self.task_threads: Dict[str, threading.Thread] = {}
        
        # Setup logging
        self.logger = logging.getLogger(__name__)
        
    def start(self):
        """Start the task scheduler"""
        if self.running:
            self.logger.warning("Scheduler is already running")
            return
            
        self.running = True
        self.scheduler_thread = threading.Thread(target=self._scheduler_loop, daemon=True)
        self.scheduler_thread.start()
        self.logger.info("Task scheduler started")
    
    def stop(self):
        """Stop the task scheduler"""
        if not self.running:
            return
            
        self.running = False
        
        # Wait for scheduler thread to finish
        if self.scheduler_thread and self.scheduler_thread.is_alive():
            self.scheduler_thread.join(timeout=5)
        
        # Stop all running task threads
        for task_id, thread in self.task_threads.items():
            if thread.is_alive():
                self.logger.info(f"Waiting for task {task_id} to complete...")
                thread.join(timeout=10)
        
        self.task_threads.clear()
        self.logger.info("Task scheduler stopped")
    
    def _scheduler_loop(self):
        """Main scheduler loop"""
        self.logger.info("Scheduler loop started")
        
        while self.running:
            try:
                # Check for ready tasks
                ready_tasks = self.config_manager.get_ready_tasks()
                
                for task_id, task in ready_tasks:
                    if not self.running:
                        break
                        
                    # Skip if task is already running
                    if task_id in self.task_threads and self.task_threads[task_id].is_alive():
                        continue
                    
                    # Start task execution in separate thread
                    self._start_task_execution(task_id, task)
                
                # Clean up completed threads
                self._cleanup_completed_threads()
                
                # Sleep for a short interval
                time.sleep(1)
                
            except Exception as e:
                self.logger.error(f"Error in scheduler loop: {e}")
                time.sleep(5)  # Wait longer on error
    
    def _start_task_execution(self, task_id: str, task: AppTask):
        """Start execution of a specific task"""
        def execute_task():
            try:
                self.logger.info(f"Starting task: {task.name} ({task_id})")
                
                # Execute the task
                success = self.app_handler.execute_task(task_id, task)
                
                # Update task statistics
                if success:
                    task.success_count += 1
                    task.last_error = None
                    self.logger.info(f"Task completed successfully: {task.name}")
                else:
                    task.error_count += 1
                    self.logger.warning(f"Task failed: {task.name}")
                
                # Update last run time and next run time
                task.update_last_run()
                
                # Save updated task
                self.config_manager.update_task(task_id, task)
                
            except Exception as e:
                error_msg = f"Task execution error: {e}"
                self.logger.error(error_msg)
                
                # Update task with error info
                task.error_count += 1
                task.last_error = str(e)
                task.update_last_run()
                self.config_manager.update_task(task_id, task)
        
        # Start task in separate thread
        task_thread = threading.Thread(target=execute_task, daemon=True)
        task_thread.start()
        self.task_threads[task_id] = task_thread
    
    def _cleanup_completed_threads(self):
        """Remove completed threads from tracking"""
        completed_tasks = []
        
        for task_id, thread in self.task_threads.items():
            if not thread.is_alive():
                completed_tasks.append(task_id)
        
        for task_id in completed_tasks:
            del self.task_threads[task_id]
    
    def execute_task_immediately(self, task_id: str) -> bool:
        """Execute a specific task immediately"""
        task = self.config_manager.get_task(task_id)
        if not task:
            self.logger.error(f"Task not found: {task_id}")
            return False
        
        if not task.enabled:
            self.logger.warning(f"Task is disabled: {task.name}")
            return False
        
        # Check if task is already running
        if task_id in self.task_threads and self.task_threads[task_id].is_alive():
            self.logger.warning(f"Task is already running: {task.name}")
            return False
        
        # Start immediate execution
        self._start_task_execution(task_id, task)
        return True
    
    def get_running_tasks(self) -> List[str]:
        """Get list of currently running task IDs"""
        running_tasks = []
        for task_id, thread in self.task_threads.items():
            if thread.is_alive():
                running_tasks.append(task_id)
        return running_tasks
    
    def is_task_running(self, task_id: str) -> bool:
        """Check if a specific task is currently running"""
        return (task_id in self.task_threads and 
                self.task_threads[task_id].is_alive())
    
    def get_scheduler_status(self) -> Dict[str, any]:
        """Get current scheduler status"""
        return {
            'running': self.running,
            'active_tasks': len([t for t in self.task_threads.values() if t.is_alive()]),
            'total_tasks': len(self.config_manager.get_all_tasks()),
            'ready_tasks': len(self.config_manager.get_ready_tasks())
        }
    
    def pause_task(self, task_id: str):
        """Pause a specific task"""
        task = self.config_manager.get_task(task_id)
        if task:
            task.enabled = False
            self.config_manager.update_task(task_id, task)
            self.logger.info(f"Task paused: {task.name}")
    
    def resume_task(self, task_id: str):
        """Resume a specific task"""
        task = self.config_manager.get_task(task_id)
        if task:
            task.enabled = True
            self.config_manager.update_task(task_id, task)
            self.logger.info(f"Task resumed: {task.name}")
    
    def reschedule_task(self, task_id: str, new_interval: int):
        """Reschedule a task with new interval"""
        task = self.config_manager.get_task(task_id)
        if task:
            task.interval_seconds = new_interval
            # Recalculate next run time
            if task.last_run:
                task.next_run = task.last_run + new_interval
            self.config_manager.update_task(task_id, task)
            self.logger.info(f"Task rescheduled: {task.name} - {new_interval}s interval")


# Setup logging configuration
def setup_logging():
    """Setup logging configuration for the scheduler"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('automation_bot.log'),
            logging.StreamHandler()
        ]
    )
