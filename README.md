# 🤖 Phần mềm quản lý ứng dụng phiên bản - Android Automation Bot

Ứng dụng Python thông minh để **tự động hóa các tác vụ lặp đi lặp lại** trên thiết bị Android sử dụng **ADB + OpenCV**. Giao diện trực quan với 3 phần chính: **Quản lý ADB**, **Danh sách ứng dụng**, và **C<PERSON><PERSON> hình tác vụ**, giúp quản lý nhiều tác vụ với bộ đếm thời gian, hỗ trợ đa thiết bị và dễ dàng mở rộng.

## 📌 Project Goals
- Automate repetitive actions on Android apps (check-ins, rewards, button clicks, etc.)
- Manage multiple applications simultaneously
- Schedule tasks with countdown timers and periodic execution
- Provide intuitive interface for progress monitoring

## 🎯 Tính Năng Chính
- ✅ **Quản lý ADB linh hoạt** - Thiết lập đường dẫn ADB tùy chỉnh, kết nối và điều khiển thiết bị Android
- ✅ **Nhận diện hình ảnh OpenCV** - Khớp mẫu thay vì tọa độ cố định
- ✅ **Giao diện 3 phần trực quan**:
  - Phần 1: Quản lý ADB và thiết bị kết nối
  - Phần 2: Danh sách ứng dụng trên thiết bị
  - Phần 3: Cấu hình tác vụ chi tiết
- ✅ **Cấu hình tác vụ linh hoạt** - Tên tác vụ, loại click (single/double), hình ảnh template
- ✅ **Hỗ trợ đa thiết bị/đa ứng dụng** - Xử lý nhiều thiết bị và tác vụ
- ✅ **Quản lý hình ảnh template** - Thêm, xóa, xem hình ảnh mẫu
- ✅ **Giao diện thân thiện** - Dễ sử dụng với hướng dẫn tiếng Việt

## 🚀 Quick Start

### Installation
```bash
# Clone the repository
git clone <repository-url>
cd autobot_android

# Install dependencies
pip install -r requirements.txt

# Run the application
python main.py
```

### Thiết Lập Tác Vụ Đầu Tiên
1. **Thiết lập ADB**: Nhập đường dẫn ADB hoặc click "Thiết lập url của adb.exe hoặc giả lập"
2. **Kết nối thiết bị**: Bật USB debugging và kết nối thiết bị Android
3. **Refresh Devices**: Click "🔄 Refresh Devices" để phát hiện thiết bị
4. **Lấy danh sách app**: Click "Lấy danh sách các app" để hiển thị ứng dụng trên thiết bị
5. **Chọn ứng dụng**: Double click vào ứng dụng trong danh sách
6. **Cấu hình tác vụ**:
   - Nhập tên tác vụ
   - Chọn loại click (Single/Double)
   - Thêm hình ảnh template cần click
7. **Lưu cấu hình**: Click "Lưu cấu hình" để hoàn tất

## � App Configuration
When clicking on each app, you can view:
- List of template images (`*.png`)
- Execution interval (countdown)
- Detailed logs: last run time, status, errors if any

## 🚀 Cài Đặt và Sử Dụng

### Cài Đặt Nhanh
```bash
# 1. Chạy script khởi tạo
python setup_bot.py

# 2. Hoặc cài đặt thủ công
pip install -r requirements.txt

# 3. Chạy ứng dụng
python main.py

# 4. Hoặc trên Windows
run_bot.bat
```

### Thiết Lập Thiết Bị Android
1. **Bật Developer Options**: Cài đặt → Thông tin điện thoại → Nhấn "Số bản dựng" 7 lần
2. **Bật USB Debugging**: Cài đặt → Developer Options → USB Debugging
3. **Kết nối thiết bị**: USB hoặc WiFi ADB
4. **Cho phép kết nối** khi thiết bị hỏi

## ⚙️ Luồng Hoạt Động

1. **Kết nối thiết bị** qua `adb devices`
2. **Chọn ứng dụng** để chạy tự động
3. **Tác vụ được lên lịch** và chạy theo vòng lặp định kỳ
4. **Chụp ảnh màn hình**, tìm kiếm ảnh mẫu, gửi lệnh tương tác
5. **Tự động lặp lại** sau thời gian đếm ngược
6. **Ghi log**, thống kê hoạt động từng app

## 🧠 Công Nghệ Sử Dụng
* ✅ **Python 3.7+** - Ngôn ngữ lập trình chính
* ✅ **ADB (Android Debug Bridge)** - Giao tiếp với thiết bị Android
* ✅ **OpenCV** - Nhận diện hình ảnh (`cv2.matchTemplate`)
* ✅ **Tkinter** - Giao diện người dùng 2 cột
* ✅ **PIL/Pillow** - Xử lý hình ảnh
* ✅ **Threading** - Xử lý đa luồng cho scheduler

## ⏰ Quản Lý Tác Vụ Theo Thời Gian
* **Interval Scheduling**: Mỗi tác vụ có `interval_seconds` → tự động chạy lại
* **Real-time Countdown**: Hiển thị thời gian còn lại trong UI
* **Task Control**: Tạm dừng/kích hoạt lại từng tác vụ
* **Smart Scheduling**: Ghi lại `last_run` và tính toán `next_run`
* **Status Tracking**: Theo dõi trạng thái và thống kê thành công/lỗi

## 📁 Cấu Trúc Dự Án

```
autobot_android/
├── 📄 main.py                 # File chính để chạy ứng dụng
├── 📄 setup_bot.py           # Script khởi tạo và kiểm tra hệ thống
├── 📄 run_bot.bat            # Script Windows để chạy nhanh
├── 📄 requirements.txt       # Danh sách thư viện Python cần thiết
├── 📄 README.md              # Tài liệu chính (file này)
├── 📄 HUONG_DAN_SU_DUNG.md  # Hướng dẫn sử dụng chi tiết
├── 📄 INSTALLATION.md        # Hướng dẫn cài đặt
│
├── 📂 src/                   # Mã nguồn chính
│   ├── 📄 __init__.py
│   ├── 📄 adb_manager.py     # Quản lý kết nối ADB
│   ├── 📄 image_matcher.py   # Nhận diện hình ảnh OpenCV
│   ├── 📄 config_manager.py  # Quản lý cấu hình và dữ liệu
│   ├── 📄 task_scheduler.py  # Lên lịch và thực thi tác vụ
│   ├── 📄 app_handler.py     # Xử lý logic tự động hóa app
│   └── 📂 gui/               # Giao diện người dùng
│       ├── 📄 __init__.py
│       ├── 📄 main_window.py # Cửa sổ chính 2 cột
│       └── 📄 app_config_dialog.py # Dialog cấu hình app
│
├── 📂 config/                # Cấu hình và dữ liệu
│   ├── 📄 settings.json      # Cài đặt ứng dụng
│   ├── 📄 tasks.json         # Danh sách tác vụ đã cấu hình
│   └── 📂 templates/         # Ảnh mẫu cho nhận diện
│       ├── 📂 common/        # Template chung (đóng quảng cáo)
│       ├── 📂 app1/          # Template cho app 1
│       └── 📂 app2/          # Template cho app 2
│
├── 📂 logs/                  # File log hoạt động
├── 📂 debug_screenshots/     # Ảnh debug khi khớp mẫu
└── 📂 backups/              # Sao lưu cấu hình
```

## 🎯 Tính Năng Chi Tiết

### 🖥️ Giao Diện 3 Phần
```
╔═══════════════════════════════════════════════════════════════════════════════╗
║                    🤖 Phần mềm quản lý ứng dụng phiên bản                     ║
╠═══════════════════════════════╦═══════════════════════════════════════════════╣
║    📱 Quản lý ADB             ║         📱 Danh sách ứng dụng trong ADB       ║
║ ┌─────────────────────────────┐║ ┌─────────────────────────────────────────────┐║
║ │ Đường dẫn ADB: [_______]    │║ │ • WhatsApp (com.whatsapp)                   │║
║ │ [Thiết lập url adb.exe]     │║ │ • Facebook (com.facebook.katana)            │║
║ │ [🔄 Refresh] [Lấy ds app]   │║ │ • Instagram (com.instagram.android)         │║
║ │ Khu vực hiển thị adb:       │║ │ • TikTok (com.zhiliaoapp.musically)         │║
║ │ - emulator-5554             │║ │ • Zalo (com.zing.zalo)                      │║
║ │ - device-12345              │║ │ ...                                         │║
║ └─────────────────────────────┘║ └─────────────────────────────────────────────┘║
╠═══════════════════════════════╩═══════════════════════════════════════════════╣
║                    📋 Cấu hình cho ứng dụng (tên ứng dụng)                    ║
║ Textbox nhập tên tác vụ: [________________]                                   ║
║ Hình thức click: ○ Single Click  ● Double Click                              ║
║ Chọn hình ảnh cần click: [Danh sách ảnh] [Thêm] [Xóa] [Xem]                 ║
║                          [Lưu cấu hình] [Hủy]                                ║
╚═══════════════════════════════════════════════════════════════════════════════╝
```

### 🎮 Điều Khiển Tác Vụ
- **🔧 Thiết lập ADB**: Thiết lập đường dẫn ADB tùy chỉnh
- **🔄 Refresh Devices**: Làm mới danh sách thiết bị
- **📱 Lấy danh sách app**: Lấy danh sách ứng dụng trên thiết bị
- **🖱️ Chọn ứng dụng**: Double click để cấu hình tác vụ
- **� Cấu hình tác vụ**: Nhập tên, chọn loại click, thêm hình ảnh
- **💾 Lưu cấu hình**: Lưu thiết lập tác vụ
- **▶️ Start Scheduler**: Bắt đầu tự động hóa
- **⏹️ Stop Scheduler**: Dừng tất cả tác vụ

## 📋 Cấu Trúc Dự Án
```
autobot_android/
├── main.py                    # Entry point - Khởi động ứng dụng
├── requirements.txt           # Dependencies - Thư viện cần thiết
├── README.md                 # Documentation - Tài liệu hướng dẫn
├── config/                   # Configuration files - File cấu hình
│   └── tasks.json           # Task configurations - Cấu hình tác vụ
├── templates/               # Template images - Hình ảnh mẫu
│   ├── app1_button.png      # Hình ảnh nút bấm ứng dụng 1
│   └── app2_icon.png        # Icon ứng dụng 2
└── src/                     # Source code - Mã nguồn
    ├── adb_manager.py       # ADB device management - Quản lý thiết bị ADB
    ├── config_manager.py    # Configuration handling - Xử lý cấu hình
    ├── task_scheduler.py    # Task scheduling logic - Logic lập lịch tác vụ
    ├── image_matcher.py     # OpenCV template matching - Khớp mẫu hình ảnh
    └── gui/                 # GUI components - Thành phần giao diện
        ├── main_window.py   # Main interface - Giao diện chính (3 phần)
        └── app_config_dialog.py # App configuration dialog - Hộp thoại cấu hình
```

## 🔧 Cài Đặt và Sử Dụng

### Yêu Cầu Hệ Thống
- **Python 3.8+**
- **Android Debug Bridge (ADB)** - Có thể tải từ Android SDK hoặc platform-tools
- **Thiết bị Android** với USB Debugging được bật
- **Thư viện Python**: OpenCV, Tkinter, Pillow

### Cài Đặt
```bash
# Clone repository
git clone https://github.com/your-repo/autobot_android.git
cd autobot_android

# Cài đặt dependencies
pip install -r requirements.txt

# Chạy ứng dụng
python main.py
```

### Hướng Dẫn Sử Dụng Chi Tiết

1. **Khởi động ứng dụng**: Chạy `python main.py`
2. **Thiết lập ADB**:
   - Nhập đường dẫn đến file `adb.exe` trong textbox
   - Hoặc click "Thiết lập url của adb.exe hoặc giả lập" để browse file
3. **Kết nối thiết bị**:
   - Kết nối thiết bị Android qua USB
   - Bật USB Debugging trong Developer Options
   - Click "🔄 Refresh Devices" để phát hiện thiết bị
4. **Lấy danh sách ứng dụng**:
   - Chọn thiết bị từ danh sách
   - Click "Lấy danh sách các app" để hiển thị ứng dụng
5. **Cấu hình tác vụ**:
   - Double click vào ứng dụng muốn cấu hình
   - Nhập tên tác vụ
   - Chọn loại click (Single/Double)
   - Thêm hình ảnh template bằng "Thêm ảnh"
   - Click "Lưu cấu hình"
6. **Chạy automation**: Click "▶️ Start Scheduler"