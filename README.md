# 🤖 Android Automation Bot

A smart Python application for **automating repetitive tasks** on Android devices using **ADB + OpenCV**. Features an intuitive two-column interface: **ADB devices** and **app list**, helping manage multiple tasks with countdown timers, multi-device support, and easy extensibility.

## 📌 Project Goals
- Automate repetitive actions on Android apps (check-ins, rewards, button clicks, etc.)
- Manage multiple applications simultaneously
- Schedule tasks with countdown timers and periodic execution
- Provide intuitive interface for progress monitoring

## 🎯 Key Features
- ✅ **ADB Device Management** - Connect and control Android devices via ADB
- ✅ **OpenCV Image Recognition** - Template matching instead of fixed coordinates
- ✅ **Two-Column GUI Interface**:
  - Column 1: Connected Android devices
  - Column 2: Configured apps sorted by next run time
- ✅ **Task Configuration** - Interval settings, template images, activity logs
- ✅ **Multi-device/Multi-app Support** - Handle multiple devices and tasks
- ✅ **Pause/Resume Tasks** - Individual task control
- ✅ **Automatic Ad Closing** - Close ads after task completion
- ✅ **Task Management** - Add, edit, delete, pause automation tasks

## 🚀 Quick Start

### Installation
```bash
# Clone the repository
git clone <repository-url>
cd autobot_android

# Install dependencies
pip install -r requirements.txt

# Run the application
python main.py
```

### Setup Your First Task
1. **Connect Device**: Enable USB debugging and connect your Android device
2. **Refresh Devices**: Click "🔄 Refresh Devices" to detect your device
3. **Add App**: Click "➕ Add App" and configure:
   - App name and package name
   - Automation interval (in seconds)
   - Template images for target UI elements
4. **Start Automation**: Click "▶️ Start Scheduler"

## � App Configuration
When clicking on each app, you can view:
- List of template images (`*.png`)
- Execution interval (countdown)
- Detailed logs: last run time, status, errors if any

## 🚀 Cài Đặt và Sử Dụng

### Cài Đặt Nhanh
```bash
# 1. Chạy script khởi tạo
python setup_bot.py

# 2. Hoặc cài đặt thủ công
pip install -r requirements.txt

# 3. Chạy ứng dụng
python main.py

# 4. Hoặc trên Windows
run_bot.bat
```

### Thiết Lập Thiết Bị Android
1. **Bật Developer Options**: Cài đặt → Thông tin điện thoại → Nhấn "Số bản dựng" 7 lần
2. **Bật USB Debugging**: Cài đặt → Developer Options → USB Debugging
3. **Kết nối thiết bị**: USB hoặc WiFi ADB
4. **Cho phép kết nối** khi thiết bị hỏi

## ⚙️ Luồng Hoạt Động

1. **Kết nối thiết bị** qua `adb devices`
2. **Chọn ứng dụng** để chạy tự động
3. **Tác vụ được lên lịch** và chạy theo vòng lặp định kỳ
4. **Chụp ảnh màn hình**, tìm kiếm ảnh mẫu, gửi lệnh tương tác
5. **Tự động lặp lại** sau thời gian đếm ngược
6. **Ghi log**, thống kê hoạt động từng app

## 🧠 Công Nghệ Sử Dụng
* ✅ **Python 3.7+** - Ngôn ngữ lập trình chính
* ✅ **ADB (Android Debug Bridge)** - Giao tiếp với thiết bị Android
* ✅ **OpenCV** - Nhận diện hình ảnh (`cv2.matchTemplate`)
* ✅ **Tkinter** - Giao diện người dùng 2 cột
* ✅ **PIL/Pillow** - Xử lý hình ảnh
* ✅ **Threading** - Xử lý đa luồng cho scheduler

## ⏰ Quản Lý Tác Vụ Theo Thời Gian
* **Interval Scheduling**: Mỗi tác vụ có `interval_seconds` → tự động chạy lại
* **Real-time Countdown**: Hiển thị thời gian còn lại trong UI
* **Task Control**: Tạm dừng/kích hoạt lại từng tác vụ
* **Smart Scheduling**: Ghi lại `last_run` và tính toán `next_run`
* **Status Tracking**: Theo dõi trạng thái và thống kê thành công/lỗi

## 📁 Cấu Trúc Dự Án

```
autobot_android/
├── 📄 main.py                 # File chính để chạy ứng dụng
├── 📄 setup_bot.py           # Script khởi tạo và kiểm tra hệ thống
├── 📄 run_bot.bat            # Script Windows để chạy nhanh
├── 📄 requirements.txt       # Danh sách thư viện Python cần thiết
├── 📄 README.md              # Tài liệu chính (file này)
├── 📄 HUONG_DAN_SU_DUNG.md  # Hướng dẫn sử dụng chi tiết
├── 📄 INSTALLATION.md        # Hướng dẫn cài đặt
│
├── 📂 src/                   # Mã nguồn chính
│   ├── 📄 __init__.py
│   ├── 📄 adb_manager.py     # Quản lý kết nối ADB
│   ├── 📄 image_matcher.py   # Nhận diện hình ảnh OpenCV
│   ├── 📄 config_manager.py  # Quản lý cấu hình và dữ liệu
│   ├── 📄 task_scheduler.py  # Lên lịch và thực thi tác vụ
│   ├── 📄 app_handler.py     # Xử lý logic tự động hóa app
│   └── 📂 gui/               # Giao diện người dùng
│       ├── 📄 __init__.py
│       ├── 📄 main_window.py # Cửa sổ chính 2 cột
│       └── 📄 app_config_dialog.py # Dialog cấu hình app
│
├── 📂 config/                # Cấu hình và dữ liệu
│   ├── 📄 settings.json      # Cài đặt ứng dụng
│   ├── 📄 tasks.json         # Danh sách tác vụ đã cấu hình
│   └── 📂 templates/         # Ảnh mẫu cho nhận diện
│       ├── 📂 common/        # Template chung (đóng quảng cáo)
│       ├── 📂 app1/          # Template cho app 1
│       └── 📂 app2/          # Template cho app 2
│
├── 📂 logs/                  # File log hoạt động
├── 📂 debug_screenshots/     # Ảnh debug khi khớp mẫu
└── 📂 backups/              # Sao lưu cấu hình
```

## 🎯 Tính Năng Chi Tiết

### 🖥️ Giao Diện 2 Cột
```
╔═══════════════╦════════════════════════════════════╗
║ 📱 ADB Devices ║    📱 Apps Đã Cấu Hình (Gần Nhất)    ║
╠═══════════════╬════════════════════════════════════╣
║ emulator-5554 ║ [📷] App Check-in (còn 5m30s)      ║
║ usb-device-1  ║ [🎁] App Rewards  (chạy ngay)       ║
║               ║ [🧼] App Cleanup (còn 1h02m)        ║
╚═══════════════╩════════════════════════════════════╝
```

### 🎮 Điều Khiển Tác Vụ
- **➕ Add App**: Thêm ứng dụng mới
- **✏️ Edit**: Chỉnh sửa cấu hình
- **🗑️ Delete**: Xóa ứng dụng
- **⏸️ Pause/Resume**: Tạm dừng/tiếp tục
- **▶️ Start Scheduler**: Bắt đầu tự động hóa
- **⏹️ Stop Scheduler**: Dừng tất cả tác vụ