"""
Configuration Window - <PERSON><PERSON><PERSON>n cấu hình cho từng ứng dụng
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import os
import subprocess


class ConfigWindow:
    def __init__(self, app_info, adb_manager):
        self.app_info = app_info
        self.adb_manager = adb_manager
        
        # Create new window
        self.root = tk.Toplevel()
        self.root.title(f"Cấu hình cho ứng dụng: {app_info['app_name']}")
        self.root.geometry("600x500")
        self.root.resizable(True, True)
        
        # Variables
        self.task_name_var = tk.StringVar()
        self.click_type_var = tk.StringVar(value="single")
        self.image_paths = []
        
        self.setup_ui()
        
        # Make window modal
        self.root.transient()
        self.root.grab_set()
        
    def setup_ui(self):
        """Setup the configuration UI"""
        # Main container
        main_frame = ttk.Frame(self.root, padding="15")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(2, weight=1)
        
        # Title
        title_text = f"Cấu hình cho ứng dụng: {self.app_info['app_name']}"
        title_label = ttk.Label(main_frame, text=title_text, font=("Arial", 14, "bold"))
        title_label.grid(row=0, column=0, pady=(0, 20))
        
        # Action buttons frame
        action_frame = ttk.Frame(main_frame)
        action_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 20))
        action_frame.columnconfigure(0, weight=1)
        action_frame.columnconfigure(1, weight=1)
        
        # Add and Remove task buttons
        ttk.Button(action_frame, text="(+) Icon thêm tác vụ", 
                  command=self.add_task).grid(row=0, column=0, padx=(0, 10), sticky=(tk.W, tk.E))
        ttk.Button(action_frame, text="(-) Icon xóa tác vụ", 
                  command=self.remove_task).grid(row=0, column=1, sticky=(tk.W, tk.E))
        
        # Configuration area
        config_frame = ttk.Frame(main_frame)
        config_frame.grid(row=2, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        config_frame.columnconfigure(0, weight=1)
        config_frame.columnconfigure(1, weight=1)
        config_frame.columnconfigure(2, weight=1)
        config_frame.rowconfigure(1, weight=1)
        
        # Task name section
        task_name_frame = ttk.LabelFrame(config_frame, text="Textbox nhập tên tác vụ", padding="10")
        task_name_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N), padx=(0, 5), pady=(0, 10))
        task_name_frame.columnconfigure(0, weight=1)
        
        self.task_name_entry = ttk.Entry(task_name_frame, textvariable=self.task_name_var, width=20)
        self.task_name_entry.grid(row=0, column=0, sticky=(tk.W, tk.E))
        
        # Click type section
        click_type_frame = ttk.LabelFrame(config_frame, text="Hình thức click (sigle hay double)", padding="10")
        click_type_frame.grid(row=0, column=1, sticky=(tk.W, tk.E, tk.N), padx=5, pady=(0, 10))
        
        ttk.Radiobutton(click_type_frame, text="Single Click", 
                       variable=self.click_type_var, value="single").grid(row=0, column=0, sticky=tk.W)
        ttk.Radiobutton(click_type_frame, text="Double Click", 
                       variable=self.click_type_var, value="double").grid(row=1, column=0, sticky=tk.W, pady=(5, 0))
        
        # Image selection section
        image_frame = ttk.LabelFrame(config_frame, text="Chọn hình ảnh cần click", padding="10")
        image_frame.grid(row=0, column=2, sticky=(tk.W, tk.E, tk.N), padx=(5, 0), pady=(0, 10))
        image_frame.columnconfigure(0, weight=1)
        
        ttk.Button(image_frame, text="Thêm ảnh", 
                  command=self.add_image).grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 5))
        ttk.Button(image_frame, text="Xóa ảnh", 
                  command=self.remove_image).grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 5))
        ttk.Button(image_frame, text="Xem ảnh", 
                  command=self.view_image).grid(row=2, column=0, sticky=(tk.W, tk.E))
        
        # Large content area for image list and preview
        content_frame = ttk.Frame(config_frame)
        content_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(10, 0))
        content_frame.columnconfigure(0, weight=1)
        content_frame.rowconfigure(0, weight=1)
        
        # Image list
        list_frame = ttk.LabelFrame(content_frame, text="Danh sách hình ảnh", padding="10")
        list_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        list_frame.columnconfigure(0, weight=1)
        list_frame.rowconfigure(0, weight=1)
        
        # Image listbox with scrollbar
        listbox_frame = ttk.Frame(list_frame)
        listbox_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        listbox_frame.columnconfigure(0, weight=1)
        listbox_frame.rowconfigure(0, weight=1)
        
        self.image_listbox = tk.Listbox(listbox_frame)
        self.image_listbox.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        image_scrollbar = ttk.Scrollbar(listbox_frame, orient="vertical", command=self.image_listbox.yview)
        image_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.image_listbox.configure(yscrollcommand=image_scrollbar.set)
        
        # Bottom buttons
        bottom_frame = ttk.Frame(main_frame)
        bottom_frame.grid(row=3, column=0, pady=(20, 0))
        
        ttk.Button(bottom_frame, text="Lưu cấu hình", 
                  command=self.save_config).grid(row=0, column=0, padx=(0, 10))
        ttk.Button(bottom_frame, text="Hủy", 
                  command=self.cancel).grid(row=0, column=1)
        
        # Configure root window
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        
    def add_task(self):
        """Add new task"""
        messagebox.showinfo("Thông báo", "Chức năng thêm tác vụ mới")
        
    def remove_task(self):
        """Remove selected task"""
        messagebox.showinfo("Thông báo", "Chức năng xóa tác vụ")
        
    def add_image(self):
        """Add image template for task"""
        file_path = filedialog.askopenfilename(
            title="Chọn hình ảnh template",
            filetypes=[("Image files", "*.png *.jpg *.jpeg *.bmp"), ("All files", "*.*")]
        )
        if file_path:
            # Add to list
            filename = os.path.basename(file_path)
            self.image_paths.append(file_path)
            self.image_listbox.insert(tk.END, f"{filename}")
            
    def remove_image(self):
        """Remove selected image from list"""
        selection = self.image_listbox.curselection()
        if selection:
            index = selection[0]
            self.image_listbox.delete(index)
            if index < len(self.image_paths):
                self.image_paths.pop(index)
        else:
            messagebox.showwarning("Cảnh báo", "Vui lòng chọn hình ảnh để xóa")
            
    def view_image(self):
        """View selected image"""
        selection = self.image_listbox.curselection()
        if selection:
            index = selection[0]
            if index < len(self.image_paths):
                file_path = self.image_paths[index]
                try:
                    # Open image with default system viewer
                    if os.name == 'nt':  # Windows
                        os.startfile(file_path)
                    elif os.name == 'posix':  # macOS and Linux
                        subprocess.run(['open' if sys.platform == 'darwin' else 'xdg-open', file_path])
                except Exception as e:
                    messagebox.showerror("Lỗi", f"Không thể mở hình ảnh: {e}")
        else:
            messagebox.showwarning("Cảnh báo", "Vui lòng chọn hình ảnh để xem")
            
    def save_config(self):
        """Save task configuration"""
        task_name = self.task_name_var.get().strip()
        if not task_name:
            messagebox.showwarning("Cảnh báo", "Vui lòng nhập tên tác vụ")
            return
            
        if not self.image_paths:
            messagebox.showwarning("Cảnh báo", "Vui lòng thêm ít nhất một hình ảnh template")
            return
            
        # Create task configuration
        click_type = self.click_type_var.get()
        
        config = {
            'app_name': self.app_info['app_name'],
            'package_name': self.app_info['package_name'],
            'task_name': task_name,
            'click_type': click_type,
            'image_paths': self.image_paths.copy()
        }
        
        # Here you would save the configuration to file or database
        # For now, just show success message
        messagebox.showinfo("Thành công", 
                           f"Đã lưu cấu hình:\n"
                           f"Ứng dụng: {config['app_name']}\n"
                           f"Tác vụ: {config['task_name']}\n"
                           f"Loại click: {config['click_type']}\n"
                           f"Số hình ảnh: {len(config['image_paths'])}")
        
        self.root.destroy()
        
    def cancel(self):
        """Cancel configuration"""
        self.root.destroy()
