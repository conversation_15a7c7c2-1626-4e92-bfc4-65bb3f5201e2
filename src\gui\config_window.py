"""
Configuration Window - <PERSON><PERSON>o di<PERSON>n cấu hình cho từng ứng dụng
Mỗi tác vụ chỉ click 1 hình ảnh
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import os
import subprocess
from ..database.config_manager import ConfigManager


class ConfigWindow:
    def __init__(self, app_info, adb_manager):
        self.app_info = app_info
        self.adb_manager = adb_manager
        self.config_manager = ConfigManager()

        # Create new window
        self.root = tk.Toplevel()
        self.root.title(f"Cấu hình cho ứng dụng: {app_info['app_name']}")
        self.root.geometry("800x600")
        self.root.resizable(True, True)

        # Variables
        self.task_name_var = tk.StringVar()
        self.click_type_var = tk.StringVar(value="single")
        self.current_image_path = None  # Chỉ 1 hình ảnh cho mỗi tác vụ
        self.image_path_var = tk.StringVar(value="<PERSON><PERSON><PERSON> chọn hình ảnh")
        self.existing_tasks = []  # <PERSON><PERSON> s<PERSON>ch tác vụ đã tạo

        self.setup_ui()
        self.load_existing_tasks()  # Load tác vụ đã có

        # Make window modal
        self.root.transient()
        self.root.grab_set()
        
    def setup_ui(self):
        """Setup the configuration UI"""
        # Configure root window
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        
        # Main container
        main_frame = ttk.Frame(self.root, padding="15")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(1, weight=1)
        
        # Title
        title_text = f"Cấu hình cho ứng dụng: {self.app_info['app_name']}"
        title_label = ttk.Label(main_frame, text=title_text, font=("Arial", 14, "bold"))
        title_label.grid(row=0, column=0, pady=(0, 20))
        
        # Create notebook for tabs
        notebook = ttk.Notebook(main_frame)
        notebook.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Tab 1: Danh sách tác vụ đã tạo
        tasks_tab = ttk.Frame(notebook)
        notebook.add(tasks_tab, text="📋 Danh sách tác vụ")
        self.setup_tasks_tab(tasks_tab)
        
        # Tab 2: Tạo tác vụ mới
        new_task_tab = ttk.Frame(notebook)
        notebook.add(new_task_tab, text="➕ Tạo tác vụ mới")
        self.setup_new_task_tab(new_task_tab)
    
    def setup_tasks_tab(self, parent):
        """Setup tab hiển thị danh sách tác vụ đã tạo"""
        parent.columnconfigure(0, weight=1)
        parent.rowconfigure(0, weight=1)
        
        # Main frame
        tasks_frame = ttk.Frame(parent, padding="10")
        tasks_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        tasks_frame.columnconfigure(0, weight=1)
        tasks_frame.rowconfigure(0, weight=1)
        
        # Tasks listbox with scrollbar
        listbox_frame = ttk.Frame(tasks_frame)
        listbox_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        listbox_frame.columnconfigure(0, weight=1)
        listbox_frame.rowconfigure(0, weight=1)
        
        self.tasks_listbox = tk.Listbox(listbox_frame, font=("Arial", 10))
        self.tasks_listbox.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        tasks_scrollbar = ttk.Scrollbar(listbox_frame, orient="vertical", command=self.tasks_listbox.yview)
        tasks_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.tasks_listbox.configure(yscrollcommand=tasks_scrollbar.set)
        
        # Buttons frame
        buttons_frame = ttk.Frame(tasks_frame)
        buttons_frame.grid(row=1, column=0, pady=(10, 0))
        
        ttk.Button(buttons_frame, text="🗑️ Xóa tác vụ", 
                  command=self.delete_selected_task).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(buttons_frame, text="🔄 Làm mới", 
                  command=self.load_existing_tasks).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(buttons_frame, text="▶️ Chạy tác vụ", 
                  command=self.run_selected_task).pack(side=tk.LEFT)
    
    def setup_new_task_tab(self, parent):
        """Setup tab tạo tác vụ mới"""
        parent.columnconfigure(0, weight=1)
        parent.rowconfigure(0, weight=1)
        
        # Main frame
        new_task_frame = ttk.Frame(parent, padding="10")
        new_task_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        new_task_frame.columnconfigure(1, weight=1)
        
        # Task name
        ttk.Label(new_task_frame, text="Tên tác vụ:").grid(row=0, column=0, sticky=tk.W, pady=(0, 10))
        self.task_name_entry = ttk.Entry(new_task_frame, textvariable=self.task_name_var, width=30)
        self.task_name_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(10, 0), pady=(0, 10))
        
        # Click type
        ttk.Label(new_task_frame, text="Loại click:").grid(row=1, column=0, sticky=tk.W, pady=(0, 10))
        click_frame = ttk.Frame(new_task_frame)
        click_frame.grid(row=1, column=1, sticky=tk.W, padx=(10, 0), pady=(0, 10))
        
        ttk.Radiobutton(click_frame, text="Single Click", 
                       variable=self.click_type_var, value="single").pack(side=tk.LEFT, padx=(0, 20))
        ttk.Radiobutton(click_frame, text="Double Click", 
                       variable=self.click_type_var, value="double").pack(side=tk.LEFT)
        
        # Image selection
        ttk.Label(new_task_frame, text="Hình ảnh:").grid(row=2, column=0, sticky=tk.W, pady=(0, 10))
        image_frame = ttk.Frame(new_task_frame)
        image_frame.grid(row=2, column=1, sticky=(tk.W, tk.E), padx=(10, 0), pady=(0, 10))
        image_frame.columnconfigure(0, weight=1)
        
        self.image_label = ttk.Label(image_frame, textvariable=self.image_path_var, 
                                   foreground="gray", font=("Arial", 9))
        self.image_label.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 5))
        
        image_buttons_frame = ttk.Frame(image_frame)
        image_buttons_frame.grid(row=1, column=0, sticky=tk.W)
        
        ttk.Button(image_buttons_frame, text="📁 Chọn ảnh", 
                  command=self.select_image).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(image_buttons_frame, text="👁️ Xem ảnh", 
                  command=self.view_current_image).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(image_buttons_frame, text="❌ Xóa ảnh", 
                  command=self.clear_image).pack(side=tk.LEFT)
        
        # Save button
        save_frame = ttk.Frame(new_task_frame)
        save_frame.grid(row=3, column=0, columnspan=2, pady=(20, 0))
        
        ttk.Button(save_frame, text="💾 Lưu tác vụ", 
                  command=self.save_task).pack()
    
    def load_existing_tasks(self):
        """Load danh sách tác vụ đã tạo cho app này"""
        try:
            # Get tasks for this app
            tasks = self.config_manager.get_tasks(
                app_name=self.app_info['app_name'],
                package_name=self.app_info['package_name']
            )
            
            self.existing_tasks = tasks
            self.tasks_listbox.delete(0, tk.END)
            
            if tasks:
                for task in tasks:
                    display_text = f"📋 {task['task_name']} - {task['click_type']} click"
                    self.tasks_listbox.insert(tk.END, display_text)
            else:
                self.tasks_listbox.insert(tk.END, "Chưa có tác vụ nào")
                
        except Exception as e:
            messagebox.showerror("Lỗi", f"Không thể tải danh sách tác vụ: {e}")
    
    def delete_selected_task(self):
        """Xóa tác vụ được chọn"""
        selection = self.tasks_listbox.curselection()
        if not selection:
            messagebox.showwarning("Cảnh báo", "Vui lòng chọn tác vụ cần xóa")
            return
        
        if len(self.existing_tasks) > selection[0]:
            task = self.existing_tasks[selection[0]]
            result = messagebox.askyesno("Xác nhận", 
                                       f"Bạn có chắc muốn xóa tác vụ '{task['task_name']}'?")
            if result:
                try:
                    # TODO: Implement delete task in config_manager
                    messagebox.showinfo("Thông báo", "Tính năng xóa tác vụ sẽ được thêm sau")
                    self.load_existing_tasks()  # Refresh list
                except Exception as e:
                    messagebox.showerror("Lỗi", f"Không thể xóa tác vụ: {e}")
    
    def run_selected_task(self):
        """Chạy tác vụ được chọn"""
        selection = self.tasks_listbox.curselection()
        if not selection:
            messagebox.showwarning("Cảnh báo", "Vui lòng chọn tác vụ cần chạy")
            return
        
        if len(self.existing_tasks) > selection[0]:
            task = self.existing_tasks[selection[0]]
            messagebox.showinfo("Thông báo", 
                              f"Tính năng chạy tác vụ '{task['task_name']}' sẽ được thêm sau")
    
    def select_image(self):
        """Chọn hình ảnh cho tác vụ"""
        file_path = filedialog.askopenfilename(
            title="Chọn hình ảnh template",
            filetypes=[
                ("Image files", "*.png *.jpg *.jpeg *.gif *.bmp"),
                ("All files", "*.*")
            ]
        )
        
        if file_path:
            self.current_image_path = file_path
            filename = os.path.basename(file_path)
            self.image_path_var.set(f"Đã chọn: {filename}")
            self.image_label.config(foreground="blue")
    
    def view_current_image(self):
        """Xem hình ảnh đã chọn"""
        if self.current_image_path and os.path.exists(self.current_image_path):
            try:
                if os.name == 'nt':  # Windows
                    os.startfile(self.current_image_path)
                elif os.name == 'posix':  # macOS and Linux
                    subprocess.run(['open' if os.uname().sysname == 'Darwin' else 'xdg-open', 
                                  self.current_image_path])
            except Exception as e:
                messagebox.showerror("Lỗi", f"Không thể mở hình ảnh: {e}")
        else:
            messagebox.showwarning("Cảnh báo", "Vui lòng chọn hình ảnh trước")
    
    def clear_image(self):
        """Xóa hình ảnh đã chọn"""
        self.current_image_path = None
        self.image_path_var.set("Chưa chọn hình ảnh")
        self.image_label.config(foreground="gray")
    
    def save_task(self):
        """Lưu tác vụ mới"""
        task_name = self.task_name_var.get().strip()
        if not task_name:
            messagebox.showwarning("Cảnh báo", "Vui lòng nhập tên tác vụ")
            return

        if not self.current_image_path:
            messagebox.showwarning("Cảnh báo", "Vui lòng chọn hình ảnh template")
            return

        try:
            # Create task configuration
            click_type = self.click_type_var.get()
            current_device = self.adb_manager.current_device if self.adb_manager.current_device else "unknown"

            config = {
                'app_name': self.app_info['app_name'],
                'package_name': self.app_info['package_name'],
                'task_name': task_name,
                'click_type': click_type,
                'image_path': self.current_image_path,  # Chỉ 1 hình ảnh
                'device_id': current_device,
                'emulator_name': getattr(self.adb_manager, 'current_emulator_name', 'Unknown')
            }

            # Save to database
            task_id = self.config_manager.save_task(config)

            # Cache app info for faster display
            self.config_manager.cache_app_info(
                self.app_info['package_name'],
                self.app_info['app_name']
            )

            messagebox.showinfo("Thành công", f"Đã lưu tác vụ '{task_name}' thành công!")
            
            # Reset form
            self.task_name_var.set("")
            self.clear_image()
            
            # Refresh tasks list
            self.load_existing_tasks()

        except Exception as e:
            messagebox.showerror("Lỗi", f"Không thể lưu tác vụ: {e}")
