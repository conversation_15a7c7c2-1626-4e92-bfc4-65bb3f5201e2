"""
App Configuration Dialog
Dialog for adding/editing app automation configurations
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import os
import shutil
from typing import Optional, List

from ..config_manager import Config<PERSON>anager, AppTask


class AppConfigDialog:
    """Dialog for configuring app automation tasks"""
    
    def __init__(self, parent, config_manager: ConfigManager, 
                 task: Optional[AppTask] = None, task_id: Optional[str] = None):
        self.parent = parent
        self.config_manager = config_manager
        self.task = task
        self.task_id = task_id
        self.result = None
        
        # Create dialog window
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("App Configuration")
        self.dialog.geometry("600x500")
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # Center the dialog
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (600 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (500 // 2)
        self.dialog.geometry(f"600x500+{x}+{y}")
        
        self.setup_ui()
        
        # Load existing task data if editing
        if self.task:
            self.load_task_data()
        
        # Wait for dialog to close
        self.dialog.wait_window()
    
    def setup_ui(self):
        """Setup the dialog UI"""
        main_frame = ttk.Frame(self.dialog, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configure grid weights
        self.dialog.columnconfigure(0, weight=1)
        self.dialog.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        row = 0
        
        # App Name
        ttk.Label(main_frame, text="App Name:").grid(row=row, column=0, sticky=tk.W, pady=5)
        self.name_var = tk.StringVar()
        ttk.Entry(main_frame, textvariable=self.name_var, width=40).grid(
            row=row, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))
        row += 1
        
        # Package Name
        ttk.Label(main_frame, text="Package Name:").grid(row=row, column=0, sticky=tk.W, pady=5)
        self.package_var = tk.StringVar()
        ttk.Entry(main_frame, textvariable=self.package_var, width=40).grid(
            row=row, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))
        row += 1
        
        # Interval
        ttk.Label(main_frame, text="Interval (seconds):").grid(row=row, column=0, sticky=tk.W, pady=5)
        self.interval_var = tk.StringVar(value="3600")  # Default 1 hour
        ttk.Entry(main_frame, textvariable=self.interval_var, width=40).grid(
            row=row, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))
        row += 1
        
        # Enabled checkbox
        self.enabled_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(main_frame, text="Enabled", variable=self.enabled_var).grid(
            row=row, column=0, columnspan=2, sticky=tk.W, pady=5)
        row += 1
        
        # Template Images Section
        ttk.Label(main_frame, text="Template Images:", font=('Arial', 10, 'bold')).grid(
            row=row, column=0, columnspan=2, sticky=tk.W, pady=(15, 5))
        row += 1
        
        # Template list frame
        template_frame = ttk.Frame(main_frame)
        template_frame.grid(row=row, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=5)
        template_frame.columnconfigure(0, weight=1)
        template_frame.rowconfigure(0, weight=1)
        main_frame.rowconfigure(row, weight=1)
        row += 1
        
        # Template listbox with scrollbar
        list_frame = ttk.Frame(template_frame)
        list_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        list_frame.columnconfigure(0, weight=1)
        list_frame.rowconfigure(0, weight=1)
        
        self.template_listbox = tk.Listbox(list_frame, height=8)
        self.template_listbox.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.template_listbox.yview)
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.template_listbox.configure(yscrollcommand=scrollbar.set)
        
        # Template buttons
        btn_frame = ttk.Frame(template_frame)
        btn_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(5, 0))
        
        ttk.Button(btn_frame, text="Add Template", 
                  command=self.add_template).grid(row=0, column=0, padx=(0, 5))
        ttk.Button(btn_frame, text="Remove Template", 
                  command=self.remove_template).grid(row=0, column=1, padx=5)
        ttk.Button(btn_frame, text="Preview Template", 
                  command=self.preview_template).grid(row=0, column=2, padx=(5, 0))
        
        # Dialog buttons
        dialog_btn_frame = ttk.Frame(main_frame)
        dialog_btn_frame.grid(row=row, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(20, 0))
        
        ttk.Button(dialog_btn_frame, text="Save", 
                  command=self.save_config).grid(row=0, column=0, padx=(0, 10))
        ttk.Button(dialog_btn_frame, text="Cancel", 
                  command=self.cancel).grid(row=0, column=1)
        
        # Help text
        help_text = ("Tips:\n"
                    "• Package name example: com.example.app\n"
                    "• Interval in seconds (3600 = 1 hour)\n"
                    "• Add template images for automation targets\n"
                    "• Templates should be clear, unique UI elements")
        
        help_label = ttk.Label(main_frame, text=help_text, font=('Arial', 8), 
                              foreground='gray', justify=tk.LEFT)
        help_label.grid(row=row+1, column=0, columnspan=2, sticky=tk.W, pady=(10, 0))
    
    def load_task_data(self):
        """Load existing task data into the form"""
        if self.task:
            self.name_var.set(self.task.name)
            self.package_var.set(self.task.package_name)
            self.interval_var.set(str(self.task.interval_seconds))
            self.enabled_var.set(self.task.enabled)
            
            # Load template images
            for template in self.task.template_images:
                self.template_listbox.insert(tk.END, template)
    
    def add_template(self):
        """Add a template image"""
        file_path = filedialog.askopenfilename(
            title="Select Template Image",
            filetypes=[
                ("Image files", "*.png *.jpg *.jpeg"),
                ("PNG files", "*.png"),
                ("JPEG files", "*.jpg *.jpeg"),
                ("All files", "*.*")
            ]
        )
        
        if file_path:
            # Get app name for directory
            app_name = self.name_var.get().strip()
            if not app_name:
                messagebox.showwarning("Warning", "Please enter an app name first")
                return
            
            # Create app template directory
            app_template_dir = self.config_manager.create_app_template_dir(app_name)
            
            # Copy template file to app directory
            filename = os.path.basename(file_path)
            dest_path = os.path.join(app_template_dir, filename)
            
            try:
                shutil.copy2(file_path, dest_path)
                self.template_listbox.insert(tk.END, filename)
                messagebox.showinfo("Success", f"Template added: {filename}")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to copy template: {e}")
    
    def remove_template(self):
        """Remove selected template"""
        selection = self.template_listbox.curselection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a template to remove")
            return
        
        template_name = self.template_listbox.get(selection[0])
        
        if messagebox.askyesno("Confirm", f"Remove template '{template_name}'?"):
            # Remove from listbox
            self.template_listbox.delete(selection[0])
            
            # Optionally remove file (ask user)
            app_name = self.name_var.get().strip()
            if app_name:
                template_path = self.config_manager.get_template_path(app_name, template_name)
                if os.path.exists(template_path):
                    if messagebox.askyesno("Delete File", 
                                         f"Also delete the template file from disk?"):
                        try:
                            os.remove(template_path)
                        except Exception as e:
                            messagebox.showerror("Error", f"Failed to delete file: {e}")
    
    def preview_template(self):
        """Preview selected template"""
        selection = self.template_listbox.curselection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a template to preview")
            return
        
        template_name = self.template_listbox.get(selection[0])
        app_name = self.name_var.get().strip()
        
        if not app_name:
            messagebox.showwarning("Warning", "App name is required")
            return
        
        template_path = self.config_manager.get_template_path(app_name, template_name)
        
        if os.path.exists(template_path):
            # Open template image in default viewer
            try:
                import subprocess
                import sys
                
                if sys.platform.startswith('win'):
                    os.startfile(template_path)
                elif sys.platform.startswith('darwin'):
                    subprocess.run(['open', template_path])
                else:
                    subprocess.run(['xdg-open', template_path])
            except Exception as e:
                messagebox.showerror("Error", f"Failed to open template: {e}")
        else:
            messagebox.showerror("Error", f"Template file not found: {template_path}")
    
    def save_config(self):
        """Save the configuration"""
        # Validate inputs
        name = self.name_var.get().strip()
        package = self.package_var.get().strip()
        interval_str = self.interval_var.get().strip()
        
        if not name:
            messagebox.showerror("Error", "App name is required")
            return
        
        if not package:
            messagebox.showerror("Error", "Package name is required")
            return
        
        try:
            interval = int(interval_str)
            if interval <= 0:
                raise ValueError("Interval must be positive")
        except ValueError:
            messagebox.showerror("Error", "Interval must be a positive number")
            return
        
        # Get template list
        templates = []
        for i in range(self.template_listbox.size()):
            templates.append(self.template_listbox.get(i))
        
        if not templates:
            if not messagebox.askyesno("Warning", 
                                     "No templates added. Continue without templates?"):
                return
        
        # Create or update task
        if self.task:
            # Update existing task
            self.task.name = name
            self.task.package_name = package
            self.task.interval_seconds = interval
            self.task.enabled = self.enabled_var.get()
            self.task.template_images = templates
            
            self.config_manager.update_task(self.task_id, self.task)
        else:
            # Create new task
            new_task = AppTask(
                name=name,
                package_name=package,
                template_images=templates,
                interval_seconds=interval,
                enabled=self.enabled_var.get()
            )
            
            self.config_manager.add_task(new_task)
        
        self.result = True
        self.dialog.destroy()
    
    def cancel(self):
        """Cancel the dialog"""
        self.result = False
        self.dialog.destroy()
