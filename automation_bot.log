2025-06-27 19:30:01,600 - __main__ - ERROR - Application error: [WinError 2] The system cannot find the file specified
Traceback (most recent call last):
  File "main.py", line 137, in main
    app = MainWindow()
          ^^^^^^^^^^^^
  File "E:\pythonApp\autobot_android\src\gui\main_window.py", line 37, in __init__
    self.setup_ui()
  File "E:\pythonApp\autobot_android\src\gui\main_window.py", line 62, in setup_ui
    self.setup_device_panel(main_frame)
  File "E:\pythonApp\autobot_android\src\gui\main_window.py", line 92, in setup_device_panel
    self.refresh_devices()
  File "E:\pythonApp\autobot_android\src\gui\main_window.py", line 165, in refresh_devices
    devices = self.adb_manager.get_connected_devices()
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\pythonApp\autobot_android\src\adb_manager.py", line 24, in get_connected_devices
    result = subprocess.run(['adb', 'devices'],
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\python\Lib\subprocess.py", line 548, in run
    with Popen(*popenargs, **kwargs) as process:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\python\Lib\subprocess.py", line 1026, in __init__
    self._execute_child(args, executable, preexec_fn, close_fds,
  File "E:\python\Lib\subprocess.py", line 1538, in _execute_child
    hp, ht, pid, tid = _winapi.CreateProcess(executable, args,
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\.vscode\extensions\ms-python.debugpy-2025.8.0-win32-x64\bundled\libs\debugpy\_vendored\pydevd\_pydev_bundle\pydev_monkey.py", line 911, in new_CreateProcess
    return getattr(_subprocess, original_name)(app_name, cmd_line, *args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [WinError 2] The system cannot find the file specified
2025-06-27 19:32:21,491 - __main__ - ERROR - Application error: [WinError 2] The system cannot find the file specified
Traceback (most recent call last):
  File "main.py", line 137, in main
    app = MainWindow()
          ^^^^^^^^^^^^
  File "E:\pythonApp\autobot_android\src\gui\main_window.py", line 37, in __init__
    self.setup_ui()
  File "E:\pythonApp\autobot_android\src\gui\main_window.py", line 62, in setup_ui
    self.setup_device_panel(main_frame)
  File "E:\pythonApp\autobot_android\src\gui\main_window.py", line 92, in setup_device_panel
    self.refresh_devices()
  File "E:\pythonApp\autobot_android\src\gui\main_window.py", line 165, in refresh_devices
    devices = self.adb_manager.get_connected_devices()
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\pythonApp\autobot_android\src\adb_manager.py", line 24, in get_connected_devices
    result = subprocess.run(['adb', 'devices'],
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "e:\python\Lib\subprocess.py", line 548, in run
    with Popen(*popenargs, **kwargs) as process:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "e:\python\Lib\subprocess.py", line 1026, in __init__
    self._execute_child(args, executable, preexec_fn, close_fds,
  File "e:\python\Lib\subprocess.py", line 1538, in _execute_child
    hp, ht, pid, tid = _winapi.CreateProcess(executable, args,
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\.vscode\extensions\ms-python.debugpy-2025.8.0-win32-x64\bundled\libs\debugpy\_vendored\pydevd\_pydev_bundle\pydev_monkey.py", line 911, in new_CreateProcess
    return getattr(_subprocess, original_name)(app_name, cmd_line, *args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [WinError 2] The system cannot find the file specified
2025-06-27 21:33:25,677 - __main__ - ERROR - Application error: [WinError 2] The system cannot find the file specified
Traceback (most recent call last):
  File "main.py", line 137, in main
    app = MainWindow()
          ^^^^^^^^^^^^
  File "E:\pythonApp\autobot_android\src\gui\main_window.py", line 37, in __init__
    self.setup_ui()
  File "E:\pythonApp\autobot_android\src\gui\main_window.py", line 62, in setup_ui
    self.setup_device_panel(main_frame)
  File "E:\pythonApp\autobot_android\src\gui\main_window.py", line 92, in setup_device_panel
    self.refresh_devices()
  File "E:\pythonApp\autobot_android\src\gui\main_window.py", line 165, in refresh_devices
    devices = self.adb_manager.get_connected_devices()
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\pythonApp\autobot_android\src\adb_manager.py", line 24, in get_connected_devices
    result = subprocess.run(['adb', 'devices'],
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "e:\python\Lib\subprocess.py", line 548, in run
    with Popen(*popenargs, **kwargs) as process:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "e:\python\Lib\subprocess.py", line 1026, in __init__
    self._execute_child(args, executable, preexec_fn, close_fds,
  File "e:\python\Lib\subprocess.py", line 1538, in _execute_child
    hp, ht, pid, tid = _winapi.CreateProcess(executable, args,
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\.vscode\extensions\ms-python.debugpy-2025.8.0-win32-x64\bundled\libs\debugpy\_vendored\pydevd\_pydev_bundle\pydev_monkey.py", line 911, in new_CreateProcess
    return getattr(_subprocess, original_name)(app_name, cmd_line, *args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [WinError 2] The system cannot find the file specified
2025-06-27 21:56:43,889 - __main__ - ERROR - Application error: [WinError 2] The system cannot find the file specified
Traceback (most recent call last):
  File "main.py", line 137, in main
    app = MainWindow()
          ^^^^^^^^^^^^
  File "E:\pythonApp\autobot_android\src\gui\main_window.py", line 37, in __init__
    self.setup_ui()
  File "E:\pythonApp\autobot_android\src\gui\main_window.py", line 62, in setup_ui
    self.setup_device_panel(main_frame)
  File "E:\pythonApp\autobot_android\src\gui\main_window.py", line 92, in setup_device_panel
    self.refresh_devices()
  File "E:\pythonApp\autobot_android\src\gui\main_window.py", line 165, in refresh_devices
    devices = self.adb_manager.get_connected_devices()
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\pythonApp\autobot_android\src\adb_manager.py", line 24, in get_connected_devices
    result = subprocess.run(['adb', 'devices'],
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "e:\python\Lib\subprocess.py", line 548, in run
    with Popen(*popenargs, **kwargs) as process:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "e:\python\Lib\subprocess.py", line 1026, in __init__
    self._execute_child(args, executable, preexec_fn, close_fds,
  File "e:\python\Lib\subprocess.py", line 1538, in _execute_child
    hp, ht, pid, tid = _winapi.CreateProcess(executable, args,
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\.vscode\extensions\ms-python.debugpy-2025.8.0-win32-x64\bundled\libs\debugpy\_vendored\pydevd\_pydev_bundle\pydev_monkey.py", line 911, in new_CreateProcess
    return getattr(_subprocess, original_name)(app_name, cmd_line, *args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [WinError 2] The system cannot find the file specified
