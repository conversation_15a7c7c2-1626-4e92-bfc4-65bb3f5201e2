"""
Main Window GUI Module
Two-column interface for device management and app configuration
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import threading
import time
import os

from ..adb_manager import ADBManager
from ..config_manager import ConfigManager
from ..task_scheduler import TaskScheduler


class MainWindow:
    """Main application window with three-panel layout"""

    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🤖 Phần mềm quản lý <PERSON>ng dụng phiên bản")
        self.root.geometry("1200x800")

        # Initialize managers
        self.adb_manager = ADBManager()
        self.config_manager = ConfigManager()
        self.task_scheduler = TaskScheduler(self.adb_manager, self.config_manager)

        # GUI state
        self.selected_device = None
        self.selected_task_id = None
        self.selected_app = None
        self.update_thread_running = True
        self.installed_apps = []

        self.setup_ui()
        self.start_update_thread()

        # Bind close event
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
    
    def setup_ui(self):
        """Setup the main UI layout with three panels"""
        # Main container
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(1, weight=1)

        # Title
        title_label = ttk.Label(main_frame, text="🤖 Phần mềm quản lý ứng dụng phiên bản",
                               font=('Arial', 16, 'bold'))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 10))

        # Top row - ADB Management and App List
        top_frame = ttk.Frame(main_frame)
        top_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        top_frame.columnconfigure(0, weight=1)
        top_frame.columnconfigure(1, weight=1)
        top_frame.rowconfigure(0, weight=1)

        # Left panel - ADB Management
        self.setup_adb_panel(top_frame)

        # Right panel - App List
        self.setup_app_list_panel(top_frame)

        # Bottom panel - Task Configuration
        self.setup_task_config_panel(main_frame)

        # Bottom status bar
        self.setup_status_bar(main_frame)

    def setup_task_config_panel(self, parent):
        """Setup task configuration panel"""
        config_frame = ttk.LabelFrame(parent, text="Cấu hình cho ứng dụng (tên ứng dụng)", padding="5")
        config_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(10, 0))
        config_frame.columnconfigure(1, weight=1)
        config_frame.rowconfigure(2, weight=1)

        # Task name
        ttk.Label(config_frame, text="Textbox nhập tên tác vụ:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        self.task_name_var = tk.StringVar()
        self.task_name_entry = ttk.Entry(config_frame, textvariable=self.task_name_var, width=30)
        self.task_name_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 10))

        # Click type
        ttk.Label(config_frame, text="Hình thức click (sigle hay double):").grid(row=1, column=0, sticky=tk.W, padx=(0, 10), pady=(5, 0))
        self.click_type_var = tk.StringVar(value="single")
        click_frame = ttk.Frame(config_frame)
        click_frame.grid(row=1, column=1, sticky=tk.W, pady=(5, 0))

        ttk.Radiobutton(click_frame, text="Single Click", variable=self.click_type_var,
                       value="single").grid(row=0, column=0, padx=(0, 10))
        ttk.Radiobutton(click_frame, text="Double Click", variable=self.click_type_var,
                       value="double").grid(row=0, column=1)

        # Image selection
        ttk.Label(config_frame, text="Chọn hình ảnh cần click:").grid(row=2, column=0, sticky=(tk.W, tk.N), padx=(0, 10), pady=(5, 0))

        image_frame = ttk.Frame(config_frame)
        image_frame.grid(row=2, column=1, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(5, 0))
        image_frame.columnconfigure(0, weight=1)
        image_frame.rowconfigure(0, weight=1)

        # Image listbox
        self.image_listbox = tk.Listbox(image_frame, height=6)
        self.image_listbox.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 5))

        # Image control buttons
        img_btn_frame = ttk.Frame(image_frame)
        img_btn_frame.grid(row=0, column=1, sticky=(tk.N, tk.S))

        ttk.Button(img_btn_frame, text="Thêm ảnh",
                  command=self.add_image).grid(row=0, column=0, pady=(0, 5), sticky=(tk.W, tk.E))
        ttk.Button(img_btn_frame, text="Xóa ảnh",
                  command=self.remove_image).grid(row=1, column=0, pady=(0, 5), sticky=(tk.W, tk.E))
        ttk.Button(img_btn_frame, text="Xem ảnh",
                  command=self.view_image).grid(row=2, column=0, sticky=(tk.W, tk.E))

        # Save/Cancel buttons
        save_btn_frame = ttk.Frame(config_frame)
        save_btn_frame.grid(row=3, column=0, columnspan=2, pady=(10, 0))

        ttk.Button(save_btn_frame, text="Lưu cấu hình",
                  command=self.save_task_config).grid(row=0, column=0, padx=(0, 10))
        ttk.Button(save_btn_frame, text="Hủy",
                  command=self.cancel_task_config).grid(row=0, column=1)
    
    def setup_adb_panel(self, parent):
        """Setup ADB management panel"""
        adb_frame = ttk.LabelFrame(parent, text="Phần mềm quản lý ứng dụng phiên bản", padding="5")
        adb_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 5))
        adb_frame.columnconfigure(0, weight=1)
        adb_frame.rowconfigure(2, weight=1)

        # ADB Path setup
        adb_path_frame = ttk.Frame(adb_frame)
        adb_path_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 5))
        adb_path_frame.columnconfigure(0, weight=1)

        ttk.Label(adb_path_frame, text="Đường dẫn ADB:").grid(row=0, column=0, sticky=tk.W)

        path_entry_frame = ttk.Frame(adb_path_frame)
        path_entry_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(2, 0))
        path_entry_frame.columnconfigure(0, weight=1)

        self.adb_path_var = tk.StringVar(value="adb")
        self.adb_path_entry = ttk.Entry(path_entry_frame, textvariable=self.adb_path_var)
        self.adb_path_entry.grid(row=0, column=0, sticky=(tk.W, tk.E), padx=(0, 5))

        ttk.Button(path_entry_frame, text="Thiết lập url của adb.exe hoặc giả lập",
                  command=self.browse_adb_path).grid(row=0, column=1)

        # Device management buttons
        btn_frame = ttk.Frame(adb_frame)
        btn_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(5, 5))
        btn_frame.columnconfigure(0, weight=1)
        btn_frame.columnconfigure(1, weight=1)

        ttk.Button(btn_frame, text="🔄 Refresh Devices",
                  command=self.refresh_devices).grid(row=0, column=0, padx=(0, 2), sticky=(tk.W, tk.E))
        ttk.Button(btn_frame, text="Lấy danh sách các app",
                  command=self.get_app_list).grid(row=0, column=1, padx=(2, 0), sticky=(tk.W, tk.E))

        # Device listbox
        ttk.Label(adb_frame, text="Khu vực hiển thị adb:").grid(row=2, column=0, sticky=tk.W, pady=(5, 2))
        self.device_listbox = tk.Listbox(adb_frame, height=8)
        self.device_listbox.grid(row=3, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        self.device_listbox.bind('<<ListboxSelect>>', self.on_device_select)

        # Initial device refresh
        self.refresh_devices()

    def browse_adb_path(self):
        """Browse for ADB executable path"""
        file_path = filedialog.askopenfilename(
            title="Chọn file ADB executable",
            filetypes=[("Executable files", "*.exe"), ("All files", "*.*")]
        )
        if file_path:
            self.adb_path_var.set(file_path)
            self.adb_manager.set_adb_path(file_path)
            messagebox.showinfo("Thành công", f"Đã thiết lập đường dẫn ADB: {file_path}")

    def get_app_list(self):
        """Get list of installed apps on selected device"""
        if not self.selected_device:
            messagebox.showwarning("Cảnh báo", "Vui lòng chọn thiết bị trước")
            return

        # Update ADB path if changed
        adb_path = self.adb_path_var.get().strip()
        if adb_path:
            self.adb_manager.set_adb_path(adb_path)

        self.app_listbox.delete(0, tk.END)
        self.app_listbox.insert(tk.END, "Đang tải danh sách ứng dụng...")

        # Get apps in background thread
        def get_apps():
            try:
                apps = self.adb_manager.get_installed_apps()
                self.installed_apps = apps

                # Update UI in main thread
                self.root.after(0, self.update_app_listbox, apps)
            except Exception as e:
                self.root.after(0, lambda: messagebox.showerror("Lỗi", f"Không thể lấy danh sách ứng dụng: {e}"))

        import threading
        threading.Thread(target=get_apps, daemon=True).start()

    def update_app_listbox(self, apps):
        """Update app listbox with app list"""
        self.app_listbox.delete(0, tk.END)
        if apps:
            for app in apps:
                display_text = f"{app['app_name']} ({app['package_name']})"
                self.app_listbox.insert(tk.END, display_text)
            self.status_label.config(text=f"Tìm thấy {len(apps)} ứng dụng")
        else:
            self.app_listbox.insert(tk.END, "Không tìm thấy ứng dụng nào")
            self.status_label.config(text="Không có ứng dụng")

    def on_app_list_select(self, event):
        """Handle app list selection"""
        selection = self.app_listbox.curselection()
        if selection:
            index = selection[0]
            if index < len(self.installed_apps):
                app = self.installed_apps[index]
                self.selected_app = app
                # Update task config panel title
                config_frame = None
                for child in self.root.winfo_children():
                    if isinstance(child, ttk.Frame):
                        for grandchild in child.winfo_children():
                            if isinstance(grandchild, ttk.LabelFrame) and "Cấu hình cho ứng dụng" in grandchild.cget("text"):
                                config_frame = grandchild
                                break
                if config_frame:
                    config_frame.config(text=f"Cấu hình cho ứng dụng ({app['app_name']})")

    def add_image(self):
        """Add image template for task"""
        file_path = filedialog.askopenfilename(
            title="Chọn hình ảnh template",
            filetypes=[("Image files", "*.png *.jpg *.jpeg *.bmp"), ("All files", "*.*")]
        )
        if file_path:
            # Add to listbox
            filename = os.path.basename(file_path)
            self.image_listbox.insert(tk.END, f"{filename} - {file_path}")

    def remove_image(self):
        """Remove selected image from list"""
        selection = self.image_listbox.curselection()
        if selection:
            self.image_listbox.delete(selection[0])
        else:
            messagebox.showwarning("Cảnh báo", "Vui lòng chọn hình ảnh để xóa")

    def view_image(self):
        """View selected image"""
        selection = self.image_listbox.curselection()
        if selection:
            item = self.image_listbox.get(selection[0])
            if " - " in item:
                file_path = item.split(" - ", 1)[1]
                try:
                    import subprocess
                    subprocess.run(['start', file_path], shell=True, check=True)
                except:
                    messagebox.showerror("Lỗi", "Không thể mở hình ảnh")
        else:
            messagebox.showwarning("Cảnh báo", "Vui lòng chọn hình ảnh để xem")

    def save_task_config(self):
        """Save task configuration"""
        if not self.selected_app:
            messagebox.showwarning("Cảnh báo", "Vui lòng chọn ứng dụng trước")
            return

        task_name = self.task_name_var.get().strip()
        if not task_name:
            messagebox.showwarning("Cảnh báo", "Vui lòng nhập tên tác vụ")
            return

        # Get image paths
        image_paths = []
        for i in range(self.image_listbox.size()):
            item = self.image_listbox.get(i)
            if " - " in item:
                file_path = item.split(" - ", 1)[1]
                image_paths.append(file_path)

        if not image_paths:
            messagebox.showwarning("Cảnh báo", "Vui lòng thêm ít nhất một hình ảnh template")
            return

        # Create task configuration
        click_type = self.click_type_var.get()

        # Here you would save the configuration
        # For now, just show a success message
        messagebox.showinfo("Thành công",
                           f"Đã lưu cấu hình:\n"
                           f"Ứng dụng: {self.selected_app['app_name']}\n"
                           f"Tác vụ: {task_name}\n"
                           f"Loại click: {click_type}\n"
                           f"Số hình ảnh: {len(image_paths)}")

    def cancel_task_config(self):
        """Cancel task configuration"""
        self.task_name_var.set("")
        self.click_type_var.set("single")
        self.image_listbox.delete(0, tk.END)
        self.selected_app = None
    
    def setup_app_list_panel(self, parent):
        """Setup app list panel"""
        app_frame = ttk.LabelFrame(parent, text="Danh sách ứng dụng trong ADB", padding="5")
        app_frame.grid(row=0, column=1, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(5, 0))
        app_frame.columnconfigure(0, weight=1)
        app_frame.rowconfigure(1, weight=1)

        # Label
        ttk.Label(app_frame, text="Hiển thị danh sách các app trong adb ở đây:").grid(row=0, column=0, sticky=tk.W, pady=(0, 5))

        # App listbox
        self.app_listbox = tk.Listbox(app_frame, height=15)
        self.app_listbox.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        self.app_listbox.bind('<<ListboxSelect>>', self.on_app_list_select)

        # Scrollbar for app listbox
        app_scrollbar = ttk.Scrollbar(app_frame, orient=tk.VERTICAL, command=self.app_listbox.yview)
        app_scrollbar.grid(row=1, column=1, sticky=(tk.N, tk.S))
        self.app_listbox.configure(yscrollcommand=app_scrollbar.set)

        # Note label
        note_label = ttk.Label(app_frame, text="Khi double click sẽ hiển thị cấu hình cho app đó",
                              font=('Arial', 9, 'italic'))
        note_label.grid(row=2, column=0, columnspan=2, sticky=tk.W, pady=(5, 0))
    
    def setup_status_bar(self, parent):
        """Setup bottom status bar"""
        status_frame = ttk.Frame(parent)
        status_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(10, 0))
        status_frame.columnconfigure(2, weight=1)

        # Scheduler control
        ttk.Button(status_frame, text="▶️ Start Scheduler",
                  command=self.start_scheduler).grid(row=0, column=0, padx=(0, 10))
        ttk.Button(status_frame, text="⏹️ Stop Scheduler",
                  command=self.stop_scheduler).grid(row=0, column=1, padx=(0, 10))

        # Status label
        self.status_label = ttk.Label(status_frame, text="Sẵn sàng")
        self.status_label.grid(row=0, column=2, sticky=tk.W)
    
    def refresh_devices(self):
        """Refresh the device list"""
        self.device_listbox.delete(0, tk.END)
        devices = self.adb_manager.get_connected_devices()
        
        if devices:
            for device in devices:
                self.device_listbox.insert(tk.END, device)
            self.status_label.config(text=f"Found {len(devices)} device(s)")
        else:
            self.device_listbox.insert(tk.END, "No devices found")
            self.status_label.config(text="No devices connected")
    
    def on_device_select(self, event):
        """Handle device selection"""
        selection = self.device_listbox.curselection()
        if selection:
            device_id = self.device_listbox.get(selection[0])
            if device_id != "No devices found":
                self.selected_device = device_id
                self.adb_manager.select_device(device_id)
                self.update_device_info()
                self.config_manager.set_setting('selected_device', device_id)
    
    def update_device_info(self):
        """Update device information display"""
        if self.selected_device:
            info = self.adb_manager.get_device_info()
            device_info = f"Thiết bị: {info.get('model', 'Unknown')} - {info.get('device_id', 'Unknown')}"
            self.status_label.config(text=device_info)
    

    

    
    def start_scheduler(self):
        """Start the task scheduler"""
        if not self.selected_device:
            messagebox.showwarning("Warning", "Please select a device first")
            return
            
        self.task_scheduler.start()
        self.status_label.config(text="Scheduler running...")
    
    def stop_scheduler(self):
        """Stop the task scheduler"""
        self.task_scheduler.stop()
        self.status_label.config(text="Scheduler stopped")
    
    def start_update_thread(self):
        """Start background thread for UI updates"""
        def update_loop():
            while self.update_thread_running:
                try:
                    # Update status or other periodic tasks if needed
                    time.sleep(1)  # Update every second
                except:
                    break

        update_thread = threading.Thread(target=update_loop, daemon=True)
        update_thread.start()
    
    def on_closing(self):
        """Handle window closing"""
        self.update_thread_running = False
        self.task_scheduler.stop()
        self.root.destroy()
    
    def run(self):
        """Start the main GUI loop"""
        self.root.mainloop()
