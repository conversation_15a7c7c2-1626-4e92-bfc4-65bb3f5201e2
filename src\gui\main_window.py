"""
Main Window GUI Module
Two-column interface for device management and app configuration
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import threading
import time
from typing import Dict, List, Optional
import os

from ..adb_manager import ADBManager
from ..config_manager import Config<PERSON>anager, AppTask
from ..task_scheduler import TaskScheduler
from .app_config_dialog import AppConfigDialog


class MainWindow:
    """Main application window with two-column layout"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🤖 Android Automation Bot")
        self.root.geometry("1000x700")
        
        # Initialize managers
        self.adb_manager = ADBManager()
        self.config_manager = ConfigManager()
        self.task_scheduler = TaskScheduler(self.adb_manager, self.config_manager)
        
        # GUI state
        self.selected_device = None
        self.selected_task_id = None
        self.update_thread_running = True
        
        self.setup_ui()
        self.start_update_thread()
        
        # Bind close event
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
    
    def setup_ui(self):
        """Setup the main UI layout"""
        # Main container
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=2)
        main_frame.rowconfigure(1, weight=1)
        
        # Title
        title_label = ttk.Label(main_frame, text="🤖 Android Automation Bot", 
                               font=('Arial', 16, 'bold'))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 10))
        
        # Left column - Devices
        self.setup_device_panel(main_frame)
        
        # Right column - Apps
        self.setup_app_panel(main_frame)
        
        # Bottom status bar
        self.setup_status_bar(main_frame)
    
    def setup_device_panel(self, parent):
        """Setup left panel for device management"""
        device_frame = ttk.LabelFrame(parent, text="📱 ADB Devices", padding="5")
        device_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 5))
        device_frame.columnconfigure(0, weight=1)
        device_frame.rowconfigure(1, weight=1)
        
        # Refresh button
        refresh_btn = ttk.Button(device_frame, text="🔄 Refresh Devices", 
                                command=self.refresh_devices)
        refresh_btn.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 5))
        
        # Device listbox
        self.device_listbox = tk.Listbox(device_frame, height=8)
        self.device_listbox.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        self.device_listbox.bind('<<ListboxSelect>>', self.on_device_select)
        
        # Device info
        self.device_info_text = tk.Text(device_frame, height=6, wrap=tk.WORD)
        self.device_info_text.grid(row=2, column=0, sticky=(tk.W, tk.E), pady=(5, 0))
        
        # Initial device refresh
        self.refresh_devices()
    
    def setup_app_panel(self, parent):
        """Setup right panel for app management"""
        app_frame = ttk.LabelFrame(parent, text="📱 Apps Đã Cấu Hình", padding="5")
        app_frame.grid(row=1, column=1, sticky=(tk.W, tk.E, tk.N, tk.S))
        app_frame.columnconfigure(0, weight=1)
        app_frame.rowconfigure(1, weight=1)
        
        # App control buttons
        btn_frame = ttk.Frame(app_frame)
        btn_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 5))
        btn_frame.columnconfigure(0, weight=1)
        
        ttk.Button(btn_frame, text="➕ Add App", 
                  command=self.add_app).grid(row=0, column=0, padx=(0, 5))
        ttk.Button(btn_frame, text="✏️ Edit", 
                  command=self.edit_app).grid(row=0, column=1, padx=5)
        ttk.Button(btn_frame, text="🗑️ Delete", 
                  command=self.delete_app).grid(row=0, column=2, padx=5)
        ttk.Button(btn_frame, text="⏸️ Pause/Resume", 
                  command=self.toggle_app).grid(row=0, column=3, padx=(5, 0))
        
        # App treeview
        columns = ('name', 'status', 'countdown', 'last_run')
        self.app_tree = ttk.Treeview(app_frame, columns=columns, show='headings', height=15)
        
        # Configure columns
        self.app_tree.heading('name', text='App Name')
        self.app_tree.heading('status', text='Status')
        self.app_tree.heading('countdown', text='Countdown')
        self.app_tree.heading('last_run', text='Last Run')
        
        self.app_tree.column('name', width=200)
        self.app_tree.column('status', width=100)
        self.app_tree.column('countdown', width=120)
        self.app_tree.column('last_run', width=150)
        
        self.app_tree.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        self.app_tree.bind('<<TreeviewSelect>>', self.on_app_select)
        
        # Scrollbar for treeview
        scrollbar = ttk.Scrollbar(app_frame, orient=tk.VERTICAL, command=self.app_tree.yview)
        scrollbar.grid(row=1, column=1, sticky=(tk.N, tk.S))
        self.app_tree.configure(yscrollcommand=scrollbar.set)
        
        # App details
        details_frame = ttk.LabelFrame(app_frame, text="App Details", padding="5")
        details_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(5, 0))
        details_frame.columnconfigure(0, weight=1)
        
        self.app_details_text = tk.Text(details_frame, height=6, wrap=tk.WORD)
        self.app_details_text.grid(row=0, column=0, sticky=(tk.W, tk.E))
    
    def setup_status_bar(self, parent):
        """Setup bottom status bar"""
        status_frame = ttk.Frame(parent)
        status_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(10, 0))
        status_frame.columnconfigure(1, weight=1)
        
        # Scheduler control
        ttk.Button(status_frame, text="▶️ Start Scheduler", 
                  command=self.start_scheduler).grid(row=0, column=0, padx=(0, 10))
        ttk.Button(status_frame, text="⏹️ Stop Scheduler", 
                  command=self.stop_scheduler).grid(row=0, column=1, padx=(0, 10))
        
        # Status label
        self.status_label = ttk.Label(status_frame, text="Ready")
        self.status_label.grid(row=0, column=2, sticky=tk.W)
    
    def refresh_devices(self):
        """Refresh the device list"""
        self.device_listbox.delete(0, tk.END)
        devices = self.adb_manager.get_connected_devices()
        
        if devices:
            for device in devices:
                self.device_listbox.insert(tk.END, device)
            self.status_label.config(text=f"Found {len(devices)} device(s)")
        else:
            self.device_listbox.insert(tk.END, "No devices found")
            self.status_label.config(text="No devices connected")
    
    def on_device_select(self, event):
        """Handle device selection"""
        selection = self.device_listbox.curselection()
        if selection:
            device_id = self.device_listbox.get(selection[0])
            if device_id != "No devices found":
                self.selected_device = device_id
                self.adb_manager.select_device(device_id)
                self.update_device_info()
                self.config_manager.set_setting('selected_device', device_id)
    
    def update_device_info(self):
        """Update device information display"""
        if self.selected_device:
            info = self.adb_manager.get_device_info()
            info_text = f"Device: {info.get('device_id', 'Unknown')}\n"
            info_text += f"Model: {info.get('model', 'Unknown')}\n"
            info_text += f"Android: {info.get('android_version', 'Unknown')}\n"
            info_text += f"Resolution: {info.get('resolution', 'Unknown')}"
            
            self.device_info_text.delete(1.0, tk.END)
            self.device_info_text.insert(1.0, info_text)
    
    def update_app_list(self):
        """Update the app list display"""
        # Clear existing items
        for item in self.app_tree.get_children():
            self.app_tree.delete(item)
        
        # Get tasks sorted by next run time
        tasks = self.config_manager.get_tasks_sorted_by_next_run()
        
        for task_id, task in tasks:
            status = "✅ Enabled" if task.enabled else "⏸️ Paused"
            countdown = task.format_countdown()
            
            last_run = "Never"
            if task.last_run:
                last_run_time = time.strftime("%H:%M:%S", time.localtime(task.last_run))
                last_run = last_run_time
            
            self.app_tree.insert('', tk.END, iid=task_id, values=(
                task.name, status, countdown, last_run
            ))
    
    def on_app_select(self, event):
        """Handle app selection"""
        selection = self.app_tree.selection()
        if selection:
            self.selected_task_id = selection[0]
            self.update_app_details()
    
    def update_app_details(self):
        """Update app details display"""
        if self.selected_task_id:
            task = self.config_manager.get_task(self.selected_task_id)
            if task:
                details = f"Package: {task.package_name}\n"
                details += f"Interval: {task.interval_seconds} seconds\n"
                details += f"Templates: {len(task.template_images)} images\n"
                details += f"Success: {task.success_count}, Errors: {task.error_count}\n"
                if task.last_error:
                    details += f"Last Error: {task.last_error}\n"
                
                self.app_details_text.delete(1.0, tk.END)
                self.app_details_text.insert(1.0, details)
    
    def add_app(self):
        """Open dialog to add new app"""
        if not self.selected_device:
            messagebox.showwarning("Warning", "Please select a device first")
            return
            
        dialog = AppConfigDialog(self.root, self.config_manager)
        if dialog.result:
            self.update_app_list()
    
    def edit_app(self):
        """Edit selected app"""
        if not self.selected_task_id:
            messagebox.showwarning("Warning", "Please select an app to edit")
            return
            
        task = self.config_manager.get_task(self.selected_task_id)
        if task:
            dialog = AppConfigDialog(self.root, self.config_manager, task, self.selected_task_id)
            if dialog.result:
                self.update_app_list()
    
    def delete_app(self):
        """Delete selected app"""
        if not self.selected_task_id:
            messagebox.showwarning("Warning", "Please select an app to delete")
            return
            
        if messagebox.askyesno("Confirm", "Are you sure you want to delete this app?"):
            self.config_manager.remove_task(self.selected_task_id)
            self.selected_task_id = None
            self.update_app_list()
            self.app_details_text.delete(1.0, tk.END)
    
    def toggle_app(self):
        """Toggle app enabled/disabled state"""
        if not self.selected_task_id:
            messagebox.showwarning("Warning", "Please select an app")
            return
            
        task = self.config_manager.get_task(self.selected_task_id)
        if task:
            task.enabled = not task.enabled
            self.config_manager.update_task(self.selected_task_id, task)
            self.update_app_list()
    
    def start_scheduler(self):
        """Start the task scheduler"""
        if not self.selected_device:
            messagebox.showwarning("Warning", "Please select a device first")
            return
            
        self.task_scheduler.start()
        self.status_label.config(text="Scheduler running...")
    
    def stop_scheduler(self):
        """Stop the task scheduler"""
        self.task_scheduler.stop()
        self.status_label.config(text="Scheduler stopped")
    
    def start_update_thread(self):
        """Start background thread for UI updates"""
        def update_loop():
            while self.update_thread_running:
                try:
                    self.root.after(0, self.update_app_list)
                    time.sleep(1)  # Update every second
                except:
                    break
        
        update_thread = threading.Thread(target=update_loop, daemon=True)
        update_thread.start()
    
    def on_closing(self):
        """Handle window closing"""
        self.update_thread_running = False
        self.task_scheduler.stop()
        self.root.destroy()
    
    def run(self):
        """Start the main GUI loop"""
        self.root.mainloop()
