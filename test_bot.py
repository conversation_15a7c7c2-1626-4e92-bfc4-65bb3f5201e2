#!/usr/bin/env python3
"""
Script Kiểm Tra Bot Tự Động Android
Kiểm tra các chức năng cơ bản của bot
"""

import sys
import os
from pathlib import Path

# Thêm src vào Python path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

from src.adb_manager import ADBManager
from src.config_manager import ConfigManager, AppTask
from src.image_matcher import ImageMatcher


def test_adb_connection():
    """Kiểm tra kết nối ADB"""
    print("🔍 Kiểm tra kết nối ADB...")
    
    adb = ADBManager()
    devices = adb.get_connected_devices()
    
    if devices:
        print(f"✅ Tìm thấy {len(devices)} thiết bị:")
        for device in devices:
            print(f"   📱 {device}")
            
            # Chọn thiết bị đầu tiên để test
            if adb.select_device(device):
                print(f"   ✅ Đã chọn thiết bị: {device}")
                
                # L<PERSON>y thông tin thiết bị
                info = adb.get_device_info()
                print(f"   📋 Model: {info.get('model', 'Unknown')}")
                print(f"   📋 Android: {info.get('android_version', 'Unknown')}")
                print(f"   📋 Resolution: {info.get('resolution', 'Unknown')}")
                
                return adb
        
    else:
        print("❌ Không tìm thấy thiết bị nào")
        print("   Hãy đảm bảo:")
        print("   - Thiết bị đã bật USB Debugging")
        print("   - Kết nối USB hoặc WiFi ADB")
        print("   - Đã cho phép kết nối ADB")
        
    return None


def test_screenshot(adb_manager):
    """Kiểm tra chụp ảnh màn hình"""
    if not adb_manager:
        print("❌ Không có thiết bị để test screenshot")
        return False
        
    print("\n📸 Kiểm tra chụp ảnh màn hình...")
    
    try:
        screenshot = adb_manager.take_screenshot()
        if screenshot:
            # Lưu ảnh test
            test_path = "debug_screenshots/test_screenshot.png"
            screenshot.save(test_path)
            print(f"✅ Chụp ảnh thành công: {test_path}")
            print(f"   📐 Kích thước: {screenshot.width}x{screenshot.height}")
            return True
        else:
            print("❌ Không thể chụp ảnh màn hình")
            return False
            
    except Exception as e:
        print(f"❌ Lỗi khi chụp ảnh: {e}")
        return False


def test_config_manager():
    """Kiểm tra quản lý cấu hình"""
    print("\n⚙️ Kiểm tra quản lý cấu hình...")
    
    try:
        config = ConfigManager()
        
        # Test tạo task mới
        test_task = AppTask(
            name="Test App",
            package_name="com.test.app",
            template_images=["test_button.png"],
            interval_seconds=3600,
            enabled=True
        )
        
        # Thêm task
        task_id = config.add_task(test_task)
        print(f"✅ Đã tạo task test: {task_id}")
        
        # Lấy task
        retrieved_task = config.get_task(task_id)
        if retrieved_task and retrieved_task.name == "Test App":
            print("✅ Lấy task thành công")
        else:
            print("❌ Lỗi khi lấy task")
            return False
        
        # Xóa task test
        config.remove_task(task_id)
        print("✅ Đã xóa task test")
        
        # Kiểm tra settings
        confidence = config.get_setting('default_confidence', 0.8)
        print(f"✅ Settings hoạt động: confidence = {confidence}")
        
        return True
        
    except Exception as e:
        print(f"❌ Lỗi config manager: {e}")
        return False


def test_image_matcher():
    """Kiểm tra nhận diện hình ảnh"""
    print("\n🖼️ Kiểm tra nhận diện hình ảnh...")
    
    try:
        matcher = ImageMatcher()
        print(f"✅ ImageMatcher khởi tạo thành công")
        print(f"   🎯 Confidence threshold: {matcher.confidence_threshold}")
        
        # Kiểm tra có ảnh test không
        test_screenshot = "debug_screenshots/test_screenshot.png"
        if os.path.exists(test_screenshot):
            from PIL import Image
            screenshot = Image.open(test_screenshot)
            print(f"✅ Đã load ảnh test: {screenshot.width}x{screenshot.height}")
        else:
            print("⚠️  Không có ảnh test để kiểm tra matching")
        
        return True
        
    except Exception as e:
        print(f"❌ Lỗi image matcher: {e}")
        return False


def test_directories():
    """Kiểm tra cấu trúc thư mục"""
    print("\n📁 Kiểm tra cấu trúc thư mục...")
    
    required_dirs = [
        "config",
        "config/templates",
        "config/templates/common",
        "logs",
        "debug_screenshots"
    ]
    
    all_exist = True
    for directory in required_dirs:
        if os.path.exists(directory):
            print(f"   ✅ {directory}")
        else:
            print(f"   ❌ {directory} - Không tồn tại")
            all_exist = False
    
    return all_exist


def test_imports():
    """Kiểm tra import các thư viện"""
    print("\n📦 Kiểm tra thư viện...")
    
    libraries = [
        ('cv2', 'OpenCV'),
        ('numpy', 'NumPy'), 
        ('PIL', 'Pillow'),
        ('tkinter', 'Tkinter')
    ]
    
    all_ok = True
    for module, name in libraries:
        try:
            __import__(module)
            print(f"   ✅ {name}")
        except ImportError as e:
            print(f"   ❌ {name} - {e}")
            all_ok = False
    
    return all_ok


def main():
    """Hàm chính"""
    print("🧪 KIỂM TRA BOT TỰ ĐỘNG ANDROID")
    print("=" * 50)
    
    # Tạo thư mục debug nếu chưa có
    os.makedirs("debug_screenshots", exist_ok=True)
    
    results = []
    
    # Kiểm tra thư viện
    results.append(("Thư viện Python", test_imports()))
    
    # Kiểm tra thư mục
    results.append(("Cấu trúc thư mục", test_directories()))
    
    # Kiểm tra ADB
    adb_manager = test_adb_connection()
    results.append(("Kết nối ADB", adb_manager is not None))
    
    # Kiểm tra screenshot (nếu có thiết bị)
    if adb_manager:
        results.append(("Chụp ảnh màn hình", test_screenshot(adb_manager)))
    
    # Kiểm tra config manager
    results.append(("Quản lý cấu hình", test_config_manager()))
    
    # Kiểm tra image matcher
    results.append(("Nhận diện hình ảnh", test_image_matcher()))
    
    # Tổng kết
    print("\n" + "=" * 50)
    print("📊 KẾT QUẢ KIỂM TRA")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:<20} {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 Tổng kết: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 Tất cả kiểm tra đều thành công!")
        print("✅ Bot sẵn sàng sử dụng!")
    else:
        print("⚠️  Một số kiểm tra thất bại.")
        print("📋 Vui lòng khắc phục các vấn đề trước khi sử dụng bot.")
    
    print("\n🚀 Để chạy bot: python main.py")


if __name__ == "__main__":
    main()
