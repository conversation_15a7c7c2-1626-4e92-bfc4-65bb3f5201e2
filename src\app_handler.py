"""
App Handler Module
Handles individual app automation tasks and workflows
"""

import time
import logging
from typing import List, Tuple, Optional
from PIL import Image

from .adb_manager import ADBManager
from .config_manager import ConfigManager, AppTask
from .image_matcher import ImageMatcher


class AppHandler:
    """Handles automation workflows for individual apps"""
    
    def __init__(self, adb_manager: ADBManager, config_manager: ConfigManager):
        self.adb_manager = adb_manager
        self.config_manager = config_manager
        self.image_matcher = ImageMatcher()
        
        # Setup logging
        self.logger = logging.getLogger(__name__)
        
        # Configuration
        self.screenshot_delay = config_manager.get_setting('screenshot_delay', 1.0)
        self.max_retries = config_manager.get_setting('max_retries', 3)
        self.auto_close_ads = config_manager.get_setting('auto_close_ads', True)
    
    def execute_task(self, task_id: str, task: AppTask) -> bool:
        """Execute a complete automation task for an app"""
        self.logger.info(f"Executing task: {task.name}")
        
        try:
            # Step 1: Launch the app
            if not self._launch_app(task.package_name):
                return False
            
            # Step 2: Wait for app to load
            time.sleep(3)
            
            # Step 3: Execute template matching workflow
            success = self._execute_template_workflow(task)
            
            # Step 4: Close ads if enabled
            if self.auto_close_ads:
                self._close_ads()
            
            # Step 5: Return to home screen
            self.adb_manager.press_home()
            
            return success
            
        except Exception as e:
            self.logger.error(f"Error executing task {task.name}: {e}")
            return False
    
    def _launch_app(self, package_name: str) -> bool:
        """Launch the specified app"""
        self.logger.info(f"Launching app: {package_name}")
        
        # First try to go to home screen
        self.adb_manager.press_home()
        time.sleep(1)
        
        # Launch the app
        if self.adb_manager.launch_app(package_name):
            self.logger.info(f"App launched successfully: {package_name}")
            return True
        else:
            self.logger.error(f"Failed to launch app: {package_name}")
            return False
    
    def _execute_template_workflow(self, task: AppTask) -> bool:
        """Execute the template matching workflow"""
        if not task.template_images:
            self.logger.warning(f"No template images configured for task: {task.name}")
            return False
        
        success_count = 0
        total_templates = len(task.template_images)
        
        for template_name in task.template_images:
            template_path = self.config_manager.get_template_path(task.name, template_name)
            
            if self._process_template(template_path, task.name):
                success_count += 1
            
            # Small delay between template processing
            time.sleep(self.screenshot_delay)
        
        # Consider successful if at least one template was processed
        success_rate = success_count / total_templates if total_templates > 0 else 0
        self.logger.info(f"Template workflow completed: {success_count}/{total_templates} successful")
        
        return success_rate > 0
    
    def _process_template(self, template_path: str, app_name: str) -> bool:
        """Process a single template image"""
        self.logger.debug(f"Processing template: {template_path}")
        
        for attempt in range(self.max_retries):
            try:
                # Take screenshot
                screenshot = self.adb_manager.take_screenshot()
                if not screenshot:
                    self.logger.error("Failed to take screenshot")
                    continue
                
                # Find template in screenshot
                match = self.image_matcher.find_template(screenshot, template_path)
                
                if match:
                    x, y, confidence = match
                    self.logger.info(f"Template found at ({x}, {y}) with confidence {confidence:.3f}")
                    
                    # Tap on the found location
                    if self.adb_manager.tap(x, y):
                        self.logger.info(f"Successfully tapped at ({x}, {y})")
                        
                        # Save debug screenshot if needed
                        self._save_debug_screenshot(screenshot, template_path, [(x, y, confidence)])
                        
                        # Wait after tap
                        time.sleep(2)
                        return True
                    else:
                        self.logger.error(f"Failed to tap at ({x}, {y})")
                else:
                    self.logger.debug(f"Template not found (attempt {attempt + 1}/{self.max_retries})")
                
                # Wait before retry
                if attempt < self.max_retries - 1:
                    time.sleep(1)
                    
            except Exception as e:
                self.logger.error(f"Error processing template {template_path}: {e}")
        
        self.logger.warning(f"Template processing failed after {self.max_retries} attempts: {template_path}")
        return False
    
    def _close_ads(self):
        """Attempt to close common ad patterns"""
        self.logger.debug("Attempting to close ads")
        
        # Common ad close button patterns
        ad_close_patterns = [
            "close_ad.png",
            "x_button.png", 
            "skip_ad.png",
            "close_button.png"
        ]
        
        # Check for ad close buttons
        screenshot = self.adb_manager.take_screenshot()
        if not screenshot:
            return
        
        for pattern in ad_close_patterns:
            # Look for pattern in common ad close locations
            ad_template_path = self.config_manager.get_template_path("common", pattern)
            
            if self.config_manager.get_app_templates("common"):
                match = self.image_matcher.find_template(screenshot, ad_template_path, confidence=0.7)
                
                if match:
                    x, y, confidence = match
                    self.logger.info(f"Found ad close button at ({x}, {y})")
                    self.adb_manager.tap(x, y)
                    time.sleep(1)
                    return
        
        # Fallback: try common close button locations (top-right corner)
        screen_width = screenshot.width
        screen_height = screenshot.height
        
        # Try tapping top-right corner area
        close_x = int(screen_width * 0.9)
        close_y = int(screen_height * 0.1)
        
        self.logger.debug(f"Trying fallback ad close at ({close_x}, {close_y})")
        self.adb_manager.tap(close_x, close_y)
        time.sleep(1)
    
    def _save_debug_screenshot(self, screenshot: Image.Image, template_path: str, 
                              matches: List[Tuple[int, int, float]]):
        """Save debug screenshot with match highlights"""
        try:
            debug_dir = "debug_screenshots"
            import os
            os.makedirs(debug_dir, exist_ok=True)
            
            timestamp = int(time.time())
            template_name = os.path.basename(template_path).split('.')[0]
            debug_path = os.path.join(debug_dir, f"{template_name}_{timestamp}.png")
            
            self.image_matcher.save_match_visualization(
                screenshot, template_path, matches, debug_path
            )
            
            self.logger.debug(f"Debug screenshot saved: {debug_path}")
            
        except Exception as e:
            self.logger.error(f"Failed to save debug screenshot: {e}")
    
    def test_template_matching(self, task_id: str) -> dict:
        """Test template matching for a task without executing actions"""
        task = self.config_manager.get_task(task_id)
        if not task:
            return {"error": "Task not found"}
        
        results = {
            "task_name": task.name,
            "templates_tested": 0,
            "templates_found": 0,
            "matches": []
        }
        
        try:
            # Take screenshot
            screenshot = self.adb_manager.take_screenshot()
            if not screenshot:
                results["error"] = "Failed to take screenshot"
                return results
            
            # Test each template
            for template_name in task.template_images:
                template_path = self.config_manager.get_template_path(task.name, template_name)
                results["templates_tested"] += 1
                
                match = self.image_matcher.find_template(screenshot, template_path)
                if match:
                    x, y, confidence = match
                    results["templates_found"] += 1
                    results["matches"].append({
                        "template": template_name,
                        "position": (x, y),
                        "confidence": confidence
                    })
            
            return results
            
        except Exception as e:
            results["error"] = str(e)
            return results
    
    def capture_template_area(self, x: int, y: int, width: int, height: int, 
                             save_path: str) -> bool:
        """Capture a specific area of the screen as a template"""
        try:
            screenshot = self.adb_manager.take_screenshot()
            if not screenshot:
                return False
            
            # Crop the specified area
            template_image = self.image_matcher.crop_region(screenshot, x, y, width, height)
            
            # Save the template
            template_image.save(save_path)
            self.logger.info(f"Template captured and saved: {save_path}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to capture template: {e}")
            return False
