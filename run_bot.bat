@echo off
chcp 65001 >nul
title 🤖 Android Automation Bot

echo ================================
echo 🤖 ANDROID AUTOMATION BOT
echo ================================
echo.

REM Kiểm tra Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python không được tìm thấy!
    echo Vui lòng cài đặt Python 3.7+ và thêm vào PATH
    pause
    exit /b 1
)

REM Kiểm tra ADB
adb version >nul 2>&1
if errorlevel 1 (
    echo ⚠️  ADB không được tìm thấy!
    echo Vui lòng cài đặt Android SDK Platform Tools
    echo.
)

REM Kiểm tra file requirements.txt
if not exist requirements.txt (
    echo ❌ Không tìm thấy requirements.txt
    pause
    exit /b 1
)

REM Kiểm tra thư viện đã cài đặt chưa
echo 📦 Kiểm tra thư viện Python...
python -c "import cv2, numpy, PIL" >nul 2>&1
if errorlevel 1 (
    echo 📦 Cài đặt thư viện cần thiết...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo ❌ Lỗi khi cài đặt thư viện!
        pause
        exit /b 1
    )
)

REM Tạo thư mục cần thiết
if not exist config mkdir config
if not exist config\templates mkdir config\templates
if not exist config\templates\common mkdir config\templates\common
if not exist logs mkdir logs
if not exist debug_screenshots mkdir debug_screenshots

echo ✅ Môi trường đã sẵn sàng!
echo.
echo 🚀 Đang khởi động Bot Tự Động Android...
echo.

REM Chạy ứng dụng
python main.py

REM Nếu có lỗi
if errorlevel 1 (
    echo.
    echo ❌ Ứng dụng đã dừng với lỗi!
    echo Kiểm tra file log để biết thêm chi tiết.
)

echo.
echo 👋 Cảm ơn bạn đã sử dụng Android Automation Bot!
pause
