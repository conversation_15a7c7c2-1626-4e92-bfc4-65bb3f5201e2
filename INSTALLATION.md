# 🚀 Installation Guide - Android Automation Bot

This guide will help you set up the Android Automation Bot on your system.

## 📋 Prerequisites

### 1. Python Requirements
- **Python 3.7 or higher** is required
- Check your Python version: `python --version`

### 2. Android Debug Bridge (ADB)
ADB is required to communicate with Android devices.

#### Option A: Install Android SDK Platform Tools (Recommended)
1. Download from: https://developer.android.com/studio/releases/platform-tools
2. Extract the downloaded file
3. Add the platform-tools directory to your system PATH

#### Option B: Install via Package Manager
- **Windows (Chocolatey)**: `choco install adb`
- **macOS (Homebrew)**: `brew install android-platform-tools`
- **Ubuntu/Debian**: `sudo apt install android-tools-adb`

#### Verify ADB Installation
```bash
adb version
```

### 3. Android Device Setup
1. **Enable Developer Options**:
   - Go to Settings → About Phone
   - Tap "Build Number" 7 times
   - Developer Options will appear in Settings

2. **Enable USB Debugging**:
   - Go to Settings → Developer Options
   - Enable "USB Debugging"

3. **Connect Device**:
   - Connect via USB cable
   - Allow USB debugging when prompted
   - Verify connection: `adb devices`

## 🔧 Installation Steps

### Step 1: Clone or Download the Project
```bash
git clone <repository-url>
cd autobot_android
```

### Step 2: Create Virtual Environment (Recommended)
```bash
# Create virtual environment
python -m venv venv

# Activate virtual environment
# Windows:
venv\Scripts\activate
# macOS/Linux:
source venv/bin/activate
```

### Step 3: Install Dependencies
```bash
pip install -r requirements.txt
```

### Step 4: Verify Installation
```bash
python main.py --help
```

## 🏃‍♂️ Quick Start

### 1. Run the Application
```bash
python main.py
```

### 2. Connect Your Device
1. Click "🔄 Refresh Devices" in the application
2. Select your device from the list
3. Device information should appear below

### 3. Add Your First App
1. Click "➕ Add App" 
2. Fill in the app details:
   - **App Name**: Friendly name (e.g., "Daily Check-in")
   - **Package Name**: Android package (e.g., "com.example.app")
   - **Interval**: Time between runs in seconds (3600 = 1 hour)
3. Add template images for automation targets
4. Click "Save"

### 4. Start Automation
1. Click "▶️ Start Scheduler"
2. The bot will automatically run tasks based on their schedules

## 🛠️ Troubleshooting

### Common Issues

#### "No devices found"
- Ensure USB debugging is enabled
- Try different USB cable/port
- Check `adb devices` in terminal
- Restart ADB: `adb kill-server && adb start-server`

#### "ADB not found"
- Verify ADB is in your system PATH
- Restart terminal/command prompt after adding to PATH
- Try full path to ADB executable

#### "Permission denied" on Linux/macOS
- Add udev rules for your device
- Run with sudo (not recommended for regular use)

#### Template matching not working
- Ensure template images are clear and unique
- Check image format (PNG recommended)
- Adjust confidence threshold in settings
- Use debug screenshots to verify matching

### Getting Help

1. **Check Logs**: Look in `automation_bot.log` for detailed error messages
2. **Debug Mode**: Run with `python main.py --debug` for verbose logging
3. **Test Templates**: Use the template testing feature in the app
4. **Screenshots**: Check `debug_screenshots/` folder for matching visualization

## 📱 Supported Devices

- **Android 5.0+** (API level 21+)
- **USB Debugging** enabled
- **ADB access** (USB or WiFi)

### Tested Devices
- Android emulators (AVD)
- Physical Android devices
- Android-x86 virtual machines

## 🔒 Security Notes

- Only enable USB debugging when needed
- Disable USB debugging when not using the bot
- Be cautious with automation on sensitive apps
- Review template images to avoid capturing sensitive information

## 📚 Next Steps

1. Read the [User Guide](USER_GUIDE.md) for detailed usage instructions
2. Check [Template Creation Guide](TEMPLATES.md) for creating effective templates
3. Explore [Advanced Configuration](ADVANCED.md) for power user features

## 🆘 Support

If you encounter issues:
1. Check this installation guide
2. Review the troubleshooting section
3. Check existing issues in the project repository
4. Create a new issue with detailed information about your problem
