#!/usr/bin/env python3
"""
Script Khởi Tạo Bot Tự Động Android
Kiểm tra và cài đặt môi trường cần thiết
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path


def print_header():
    """In tiêu đề chương trình"""
    print("=" * 60)
    print("🤖 KHỞI TẠO BOT TỰ ĐỘNG ANDROID")
    print("=" * 60)
    print()


def check_python_version():
    """Kiểm tra phiên bản Python"""
    print("📋 Kiểm tra phiên bản Python...")
    
    if sys.version_info < (3, 7):
        print("❌ Cần Python 3.7 trở lên!")
        print(f"   Phiên bản hiện tại: {sys.version}")
        return False
    
    print(f"✅ Python {sys.version.split()[0]} - OK")
    return True


def check_adb():
    """Kiểm tra ADB có sẵn không"""
    print("\n📱 Kiểm tra Android Debug Bridge (ADB)...")
    
    try:
        result = subprocess.run(['adb', 'version'], 
                              capture_output=True, text=True, check=True)
        print("✅ ADB đã được cài đặt")
        print(f"   {result.stdout.split()[0]} {result.stdout.split()[4]}")
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("❌ ADB không tìm thấy!")
        print("   Vui lòng cài đặt Android SDK Platform Tools")
        print("   Tải về: https://developer.android.com/studio/releases/platform-tools")
        return False


def install_dependencies():
    """Cài đặt các thư viện Python cần thiết"""
    print("\n📦 Cài đặt thư viện Python...")
    
    requirements_file = Path("requirements.txt")
    if not requirements_file.exists():
        print("❌ Không tìm thấy file requirements.txt")
        return False
    
    try:
        subprocess.run([sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'], 
                      check=True)
        print("✅ Đã cài đặt thành công các thư viện")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Lỗi khi cài đặt thư viện: {e}")
        return False


def create_directories():
    """Tạo các thư mục cần thiết"""
    print("\n📁 Tạo cấu trúc thư mục...")
    
    directories = [
        "config",
        "config/templates",
        "config/templates/common",
        "logs",
        "debug_screenshots",
        "backups"
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"   📂 {directory}")
    
    print("✅ Đã tạo cấu trúc thư mục")
    return True


def copy_example_configs():
    """Sao chép file cấu hình mẫu"""
    print("\n⚙️ Tạo file cấu hình mẫu...")
    
    # Tạo file settings.json nếu chưa có
    settings_file = Path("config/settings.json")
    if not settings_file.exists():
        example_settings = Path("config/settings_example.json")
        if example_settings.exists():
            shutil.copy2(example_settings, settings_file)
            print("   📄 config/settings.json")
    
    # Tạo file tasks.json nếu chưa có
    tasks_file = Path("config/tasks.json")
    if not tasks_file.exists():
        example_tasks = Path("config/tasks_example.json")
        if example_tasks.exists():
            shutil.copy2(example_tasks, tasks_file)
            print("   📄 config/tasks.json")
        else:
            # Tạo file tasks.json trống
            with open(tasks_file, 'w', encoding='utf-8') as f:
                f.write('{}')
            print("   📄 config/tasks.json (trống)")
    
    print("✅ Đã tạo file cấu hình")
    return True


def create_common_templates():
    """Tạo thư mục template chung cho các nút đóng quảng cáo"""
    print("\n🖼️ Tạo thư mục template chung...")
    
    common_dir = Path("config/templates/common")
    common_dir.mkdir(exist_ok=True)
    
    # Tạo file README trong thư mục common
    readme_content = """# Thư mục Template Chung

Thư mục này chứa các template ảnh chung được sử dụng cho:
- Nút đóng quảng cáo (close_ad.png, x_button.png)
- Nút bỏ qua (skip_ad.png)
- Các nút điều khiển chung khác

## Hướng dẫn:
1. Chụp ảnh các nút đóng quảng cáo phổ biến
2. Lưu với tên mô tả (VD: close_ad.png, x_button.png)
3. Bot sẽ tự động sử dụng để đóng quảng cáo
"""
    
    with open(common_dir / "README.md", 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print("   📂 config/templates/common/")
    print("   📄 README.md")
    print("✅ Đã tạo thư mục template chung")
    return True


def test_imports():
    """Kiểm tra các thư viện có import được không"""
    print("\n🧪 Kiểm tra thư viện...")
    
    required_modules = [
        ('cv2', 'OpenCV'),
        ('numpy', 'NumPy'),
        ('PIL', 'Pillow'),
        ('tkinter', 'Tkinter')
    ]
    
    all_ok = True
    for module, name in required_modules:
        try:
            __import__(module)
            print(f"   ✅ {name}")
        except ImportError:
            print(f"   ❌ {name} - Chưa cài đặt")
            all_ok = False
    
    return all_ok


def check_device_connection():
    """Kiểm tra kết nối thiết bị Android"""
    print("\n📱 Kiểm tra kết nối thiết bị...")
    
    try:
        result = subprocess.run(['adb', 'devices'], 
                              capture_output=True, text=True, check=True)
        lines = result.stdout.strip().split('\n')[1:]  # Bỏ qua dòng tiêu đề
        
        devices = []
        for line in lines:
            if line.strip() and '\tdevice' in line:
                device_id = line.split('\t')[0]
                devices.append(device_id)
        
        if devices:
            print(f"✅ Tìm thấy {len(devices)} thiết bị:")
            for device in devices:
                print(f"   📱 {device}")
        else:
            print("⚠️  Không tìm thấy thiết bị nào")
            print("   Hãy đảm bảo:")
            print("   - Thiết bị đã bật USB Debugging")
            print("   - Kết nối USB hoặc WiFi ADB")
            print("   - Đã cho phép kết nối ADB trên thiết bị")
        
        return len(devices) > 0
        
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("❌ Không thể kiểm tra thiết bị (ADB lỗi)")
        return False


def print_next_steps():
    """In hướng dẫn bước tiếp theo"""
    print("\n" + "=" * 60)
    print("🎉 KHỞI TẠO HOÀN TẤT!")
    print("=" * 60)
    print()
    print("📋 Bước tiếp theo:")
    print("1. Chạy ứng dụng:")
    print("   python main.py")
    print()
    print("2. Kết nối thiết bị Android:")
    print("   - Bật USB Debugging")
    print("   - Kết nối thiết bị")
    print("   - Nhấn 'Refresh Devices' trong app")
    print()
    print("3. Thêm ứng dụng cần tự động hóa:")
    print("   - Nhấn 'Add App'")
    print("   - Cấu hình thông tin app")
    print("   - Thêm ảnh mẫu")
    print()
    print("4. Bắt đầu tự động hóa:")
    print("   - Nhấn 'Start Scheduler'")
    print()
    print("📚 Tài liệu:")
    print("   - HUONG_DAN_SU_DUNG.md")
    print("   - INSTALLATION.md")
    print()


def main():
    """Hàm chính"""
    print_header()
    
    success = True
    
    # Kiểm tra các yêu cầu cơ bản
    if not check_python_version():
        success = False
    
    if not check_adb():
        success = False
    
    # Tạo cấu trúc dự án
    if success:
        create_directories()
        copy_example_configs()
        create_common_templates()
    
    # Cài đặt thư viện
    if success:
        if not install_dependencies():
            success = False
    
    # Kiểm tra thư viện
    if success:
        if not test_imports():
            success = False
    
    # Kiểm tra thiết bị (không bắt buộc)
    check_device_connection()
    
    # Kết luận
    if success:
        print_next_steps()
    else:
        print("\n❌ Khởi tạo không thành công!")
        print("Vui lòng khắc phục các lỗi trên và chạy lại script.")
        sys.exit(1)


if __name__ == "__main__":
    main()
