# 📖 Hướng Dẫn Sử Dụng - Bot Tự Động Android

## 🚀 Bắt Đầu Nhanh

### 1. <PERSON>ài Đặt và Chạy Ứng Dụng
```bash
# Cài đặt các thư viện cần thiết
pip install -r requirements.txt

# Chạy ứng dụng
python main.py
```

### 2. Kết Nối Thiết Bị Android
1. **Bật chế độ Developer** trên thiết bị Android:
   - Vào Cài đặt → Thông tin điện thoại
   - Nhấn "Số bản dựng" 7 lần
   - Chế độ Developer sẽ xuất hiện trong Cài đặt

2. **Bật USB Debugging**:
   - Vào Cài đặt → Tùy chọn Developer
   - Bật "USB Debugging"

3. **Kết nối thiết bị**:
   - Kết nối qua cáp USB
   - Cho phép USB debugging khi được hỏi
   - Nhấn "🔄 Refresh Devices" trong ứng dụng

### 3. Thêm Ứng Dụng Đầu Tiên
1. <PERSON><PERSON><PERSON><PERSON> "➕ Add App"
2. Điền thông tin:
   - **Tên App**: Tên hiển thị (VD: "Điểm danh hàng ngày")
   - **Package Name**: Tên gói Android (VD: "com.example.app")
   - **Interval**: Thời gian giữa các lần chạy (giây)
3. Thêm ảnh mẫu cho các mục tiêu tự động hóa
4. Nhấn "Save"

## 🎯 Cách Tạo Ảnh Mẫu Hiệu Quả

### Nguyên Tắc Cơ Bản
- **Chọn vùng độc đáo**: Tránh các vùng có thể thay đổi (thời gian, số liệu)
- **Kích thước vừa phải**: Không quá nhỏ (khó nhận diện) hoặc quá lớn (chậm)
- **Chất lượng cao**: Ảnh rõ nét, không bị mờ
- **Định dạng PNG**: Khuyến nghị sử dụng PNG để giữ chất lượng

### Quy Trình Tạo Mẫu
1. **Chụp ảnh màn hình** khi ứng dụng hiển thị nút/vùng cần nhấn
2. **Cắt vùng cần thiết** bằng công cụ chỉnh sửa ảnh
3. **Lưu với tên mô tả** (VD: "nut_diem_danh.png", "nhan_qua.png")
4. **Thêm vào cấu hình** app trong bot

### Ví Dụ Ảnh Mẫu Tốt
```
✅ Tốt:
- Nút "Điểm danh" với viền rõ ràng
- Icon quà tặng độc đáo
- Text "Nhận thưởng" với font đặc biệt

❌ Tránh:
- Vùng có số liệu thay đổi (coin, điểm)
- Background phức tạp
- Vùng quá nhỏ hoặc quá mờ
```

## ⚙️ Cấu Hình Chi Tiết

### Thông Số Quan Trọng
- **Interval (Khoảng thời gian)**:
  - 3600 = 1 giờ
  - 86400 = 1 ngày
  - 300 = 5 phút

- **Confidence (Độ tin cậy)**:
  - 0.8 = Mặc định (khuyến nghị)
  - 0.9 = Nghiêm ngặt hơn
  - 0.7 = Lỏng lẻo hơn

### Quản Lý Tác Vụ
- **▶️ Start Scheduler**: Bắt đầu chạy tự động
- **⏹️ Stop Scheduler**: Dừng tất cả tác vụ
- **⏸️ Pause/Resume**: Tạm dừng/tiếp tục từng app
- **✏️ Edit**: Chỉnh sửa cấu hình app
- **🗑️ Delete**: Xóa app khỏi danh sách

## 🔧 Xử Lý Sự Cố

### Thiết Bị Không Được Nhận Diện
```bash
# Kiểm tra kết nối ADB
adb devices

# Khởi động lại ADB nếu cần
adb kill-server
adb start-server
```

### Ảnh Mẫu Không Khớp
1. **Kiểm tra độ phân giải**: Đảm bảo ảnh mẫu phù hợp với màn hình
2. **Điều chỉnh confidence**: Giảm xuống 0.7 nếu quá nghiêm ngặt
3. **Xem debug screenshots**: Kiểm tra thư mục `debug_screenshots/`
4. **Tạo lại mẫu**: Chụp ảnh mẫu mới trong điều kiện tương tự

### Ứng Dụng Không Mở
- **Kiểm tra package name**: Đảm bảo đúng tên gói
- **Cài đặt app**: Đảm bảo app đã được cài trên thiết bị
- **Quyền truy cập**: Một số app cần quyền đặc biệt

## 📊 Theo Dõi Hoạt Động

### Thông Tin Hiển Thị
- **Status**: Trạng thái hiện tại (Enabled/Paused)
- **Countdown**: Thời gian còn lại đến lần chạy tiếp
- **Last Run**: Thời gian chạy gần nhất
- **Success/Error Count**: Số lần thành công/lỗi

### Log Files
- **automation_bot.log**: Log chi tiết hoạt động
- **debug_screenshots/**: Ảnh debug khi khớp mẫu

## 🎮 Các Tính Năng Nâng Cao

### Tự Động Tắt Quảng Cáo
- Bot sẽ tự động tìm và nhấn nút đóng quảng cáo
- Có thể tắt tính năng này trong settings

### Đa Thiết Bị
- Hỗ trợ kết nối nhiều thiết bị cùng lúc
- Chọn thiết bị khác nhau cho các app khác nhau

### Template Testing
- Kiểm tra ảnh mẫu mà không thực hiện hành động
- Hữu ích để debug và tinh chỉnh

## 💡 Mẹo Sử Dụng Hiệu Quả

### Lên Lịch Thông Minh
- **App điểm danh**: Chạy mỗi 24 giờ vào sáng sớm
- **Nhận quà**: Chạy mỗi 4-6 giờ
- **Mini game**: Chạy mỗi 30 phút - 1 giờ

### Tối Ưu Hiệu Suất
- Không chạy quá nhiều app cùng lúc
- Sử dụng ảnh mẫu nhỏ gọn
- Kiểm tra log thường xuyên

### Bảo Mật
- Chỉ bật USB debugging khi cần thiết
- Không sử dụng trên app ngân hàng/thanh toán
- Kiểm tra ảnh mẫu để tránh chụp thông tin nhạy cảm

## 🆘 Hỗ Trợ

Nếu gặp vấn đề:
1. Kiểm tra file log `automation_bot.log`
2. Chạy với chế độ debug: `python main.py --debug`
3. Xem thư mục `debug_screenshots/` để kiểm tra
4. Tham khảo phần xử lý sự cố ở trên
