# ⚡ Hướng Dẫn Nhanh - Bo<PERSON> Tự Động Android

## 🚀 Bắt Đầu Trong 5 Phút

### Bước 1: Cài Đặt
```bash
# Tự động cài đặt và kiểm tra
python setup_bot.py

# Hoặc trên Windows
run_bot.bat
```

### Bước 2: Thi<PERSON>t Lập Thiết Bị
1. **Bật Developer Options** trên Android:
   - Cài đặt → Thông tin điện thoại → N<PERSON><PERSON>n "Số bản dựng" 7 lần

2. **Bật USB Debugging**:
   - Cài đặt → Developer Options → USB Debugging ✅

3. **Kết nối thiết bị** và cho phép ADB

### Bước 3: Chạy Bot
```bash
python main.py
```

### Bước 4: Cấu Hình App Đầu Tiên
1. **Refresh Devices** → Chọn thiết bị
2. **Add App** → Đ<PERSON><PERSON><PERSON> thông tin:
   - Tên: "<PERSON>i<PERSON><PERSON> danh hàng ngày"
   - Package: `com.example.app`
   - Interval: `86400` (24 giờ)
3. **Thêm ảnh mẫu** cho nút cần nhấn
4. **Save** → **Start Scheduler**

## 📱 Tìm Package Name

### Cách 1: ADB Command
```bash
# Mở app trên thiết bị, sau đó chạy:
adb shell dumpsys window | grep -E 'mCurrentFocus'
```

### Cách 2: App Info
- Cài đặt → Apps → Chọn app → Advanced → App details

### Cách 3: Play Store URL
- URL: `https://play.google.com/store/apps/details?id=PACKAGE_NAME`

## 🖼️ Tạo Ảnh Mẫu

### Quy Tắc Vàng
- ✅ **Chọn vùng độc đáo** (nút, icon đặc biệt)
- ✅ **Kích thước 50-200px** (vừa phải)
- ✅ **Định dạng PNG** (chất lượng cao)
- ❌ **Tránh vùng thay đổi** (số liệu, thời gian)

### Quy Trình
1. Chụp ảnh màn hình khi app hiển thị nút
2. Cắt vùng nút bằng Paint/Photoshop
3. Lưu với tên mô tả: `nut_diem_danh.png`
4. Thêm vào bot

## ⏰ Thiết Lập Thời Gian

### Interval Phổ Biến
```
300     = 5 phút
1800    = 30 phút  
3600    = 1 giờ
14400   = 4 giờ
86400   = 24 giờ (1 ngày)
```

### Lời Khuyên
- **Điểm danh**: 24 giờ (86400s)
- **Nhận quà**: 4-6 giờ (14400-21600s)
- **Mini game**: 30 phút - 1 giờ (1800-3600s)

## 🔧 Xử Lý Sự Cố Nhanh

### Thiết Bị Không Nhận Diện
```bash
adb kill-server
adb start-server
adb devices
```

### Ảnh Mẫu Không Khớp
1. Giảm confidence xuống 0.7
2. Tạo lại ảnh mẫu rõ hơn
3. Kiểm tra `debug_screenshots/`

### App Không Mở
- Kiểm tra package name đúng chưa
- App có cài trên thiết bị không
- Thử mở app thủ công trước

## 🎯 Ví Dụ Cấu Hình

### App Điểm Danh
```
Tên: Điểm danh TikTok
Package: com.zhiliaoapp.musically
Interval: 86400 (24h)
Templates: 
  - nut_diem_danh.png
  - xac_nhan.png
```

### Game Nhận Quà
```
Tên: Coin Master
Package: com.moonactive.coinmaster
Interval: 14400 (4h)
Templates:
  - nut_nhan_qua.png
  - dong_popup.png
```

## 📊 Theo Dõi Hoạt Động

### Trong App
- **Status**: Enabled/Paused
- **Countdown**: Thời gian còn lại
- **Last Run**: Lần chạy cuối
- **Success/Error**: Thống kê

### Log Files
- `automation_bot.log`: Log chi tiết
- `debug_screenshots/`: Ảnh debug

## 💡 Mẹo Pro

### Tối Ưu Hiệu Suất
- Không chạy quá 5 app cùng lúc
- Sử dụng ảnh mẫu nhỏ gọn
- Kiểm tra log thường xuyên

### Bảo Mật
- Chỉ dùng cho app giải trí
- Không dùng cho app ngân hàng
- Tắt USB debugging khi không dùng

### Backup
- Sao lưu `config/` thường xuyên
- Export cấu hình quan trọng

## 🆘 Cần Hỗ Trợ?

1. **Kiểm tra hệ thống**: `python test_bot.py`
2. **Xem log**: `automation_bot.log`
3. **Debug mode**: `python main.py --debug`
4. **Đọc tài liệu**: `HUONG_DAN_SU_DUNG.md`

---
**🎉 Chúc bạn tự động hóa thành công!**
