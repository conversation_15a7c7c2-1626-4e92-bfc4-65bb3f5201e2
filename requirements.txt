# Android Automation Bot Dependencies

# Core dependencies
opencv-python>=4.8.0
numpy>=1.24.0
Pillow>=10.0.0

# ADB and Android automation
pure-python-adb>=0.3.0

# GUI framework
tkinter-tooltip>=2.0.0

# Configuration and data handling
pyyaml>=6.0
jsonschema>=4.17.0

# Logging and utilities
colorlog>=6.7.0
python-dateutil>=2.8.2

# Optional: OCR support (advanced feature)
# pytesseract>=0.3.10
# Uncomment above line if OCR functionality is needed

# Development dependencies (optional)
# pytest>=7.4.0
# black>=23.0.0
# flake8>=6.0.0
