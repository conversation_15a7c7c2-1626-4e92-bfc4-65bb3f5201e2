# 🎮 Hướng Dẫn Kết Nối LDPlayer

## 📋 Tổng Quan
LDPlayer là một emulator Android phổ biến cho PC. Ứng dụng này hỗ trợ kết nối với LDPlayer để tự động hóa các tác vụ trên Android.

## 🔧 Cài Đặt và Chuẩn Bị

### 1. Cài Đặt LDPlayer
- Tải LDPlayer từ trang chủ: https://www.ldplayer.net/
- Cài đặt theo hướng dẫn
- Khởi động LDPlayer và thiết lập Android

### 2. Bật ADB Debugging
1. Trong LDPlayer, mở **Settings** (Cài đặt)
2. Tìm **Developer Options** (T<PERSON><PERSON> chọn nhà phát triển)
3. Bật **USB Debugging** (Gỡ lỗi USB)
4. Bật **ADB Debugging** nếu có

### 3. Kiểm Tra Cổng Kết Nối
LDPlayer sử dụng các cổng sau để kết nối ADB:
- **LDPlayer Instance 0**: 127.0.0.1:5555
- **LDPlayer Instance 1**: 127.0.0.1:5556
- **LDPlayer Instance 2**: 127.0.0.1:5557
- ...và tiếp tục

## 🚀 Sử Dụng Với Ứng Dụng

### Phương Pháp 1: Tự Động Phát Hiện (Khuyến Nghị)
1. Khởi động LDPlayer trước
2. Mở ứng dụng Android Automation Bot
3. Click **"Tự động phát hiện LDPlayer"**
4. Ứng dụng sẽ tự động:
   - Tìm đường dẫn ADB của LDPlayer
   - Kết nối với các instance đang chạy
   - Hiển thị danh sách thiết bị

### Phương Pháp 2: Thiết Lập Thủ Công
1. Tìm thư mục cài đặt LDPlayer (thường là):
   ```
   C:\LDPlayer\LDPlayer4.0\adb.exe
   C:\LDPlayer\LDPlayer9\adb.exe
   C:\LDPlayer\LDPlayer64\adb.exe
   ```
2. Click **"Button thiết lập url của adb.exe hoặc giả lập"**
3. Chọn file `adb.exe` trong thư mục LDPlayer
4. Click **"Button lấy danh sách các app"**

## 🔍 Xử Lý Sự Cố

### Lỗi: Không Tìm Thấy Thiết Bị
**Nguyên nhân:**
- LDPlayer chưa khởi động
- ADB Debugging chưa được bật
- Cổng kết nối bị chặn

**Giải pháp:**
1. Đảm bảo LDPlayer đang chạy
2. Kiểm tra USB Debugging trong Settings
3. Thử khởi động lại LDPlayer
4. Kiểm tra firewall/antivirus

### Lỗi: Kết Nối Bị Từ Chối
**Nguyên nhân:**
- Cổng ADB bị chiếm dụng
- Xung đột với ADB khác

**Giải pháp:**
1. Đóng tất cả ứng dụng sử dụng ADB khác
2. Khởi động lại LDPlayer
3. Sử dụng lệnh thủ công:
   ```cmd
   adb kill-server
   adb start-server
   adb connect 127.0.0.1:5555
   ```

### Lỗi: Không Lấy Được Danh Sách App
**Nguyên nhân:**
- Thiết bị chưa được chọn
- Quyền truy cập bị hạn chế

**Giải pháp:**
1. Chọn thiết bị trong danh sách trước
2. Đảm bảo LDPlayer đã boot xong
3. Thử cài đặt một vài ứng dụng test

## 📱 Quản Lý Nhiều Instance

### Sử Dụng LDMultiPlayer
1. Mở **LDMultiPlayer** (đi kèm với LDPlayer)
2. Tạo nhiều instance Android
3. Khởi động các instance cần sử dụng
4. Ứng dụng sẽ tự động phát hiện tất cả instance

### Kết Nối Thủ Công
Nếu cần kết nối thủ công với instance cụ thể:
```cmd
# Kết nối với instance 0
adb connect 127.0.0.1:5555

# Kết nối với instance 1  
adb connect 127.0.0.1:5556

# Kiểm tra danh sách thiết bị
adb devices
```

## ⚙️ Cấu Hình Nâng Cao

### Thay Đổi Cổng ADB
1. Mở LDPlayer Settings
2. Tìm **Advanced Settings**
3. Thay đổi **ADB Port** nếu cần
4. Khởi động lại LDPlayer

### Tối Ưu Hiệu Suất
1. **CPU**: Cấp phát đủ CPU cores cho LDPlayer
2. **RAM**: Tối thiểu 2GB, khuyến nghị 4GB+
3. **Graphics**: Bật Hardware Acceleration
4. **Network**: Sử dụng Bridge Mode nếu cần

## 🎯 Mẹo Sử Dụng

### Kiểm Tra Kết Nối
Trước khi sử dụng ứng dụng, kiểm tra kết nối:
```cmd
adb devices
```
Kết quả mong muốn:
```
List of devices attached
127.0.0.1:5555  device
```

### Backup Cấu Hình
- Sao lưu cấu hình LDPlayer thường xuyên
- Export/Import instance settings khi cần

### Tự Động Hóa Khởi Động
Tạo batch file để tự động khởi động:
```batch
@echo off
start "" "C:\LDPlayer\LDPlayer4.0\LDPlayer.exe"
timeout /t 30
adb connect 127.0.0.1:5555
echo LDPlayer ready!
pause
```

## 📞 Hỗ Trợ

Nếu gặp vấn đề:
1. Kiểm tra log trong ứng dụng
2. Thử các bước xử lý sự cố ở trên
3. Khởi động lại cả LDPlayer và ứng dụng
4. Liên hệ hỗ trợ kỹ thuật

---
**Lưu ý**: Hướng dẫn này áp dụng cho LDPlayer phiên bản 4.0, 9.0 và 64-bit. Các phiên bản khác có thể có sự khác biệt nhỏ.
