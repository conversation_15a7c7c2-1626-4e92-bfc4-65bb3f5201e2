"""
Configuration Manager for Android Automation Bot
Handles SQLite database operations for storing tasks and configurations
"""

import sqlite3
import json
import os
from datetime import datetime
from typing import List, Dict, Optional, Any
from pathlib import Path

class ConfigManager:
    def __init__(self, db_path: str = "config/automation_bot.db"):
        """Initialize configuration manager with SQLite database"""
        self.db_path = db_path
        
        # Create config directory if not exists
        os.makedirs(os.path.dirname(db_path), exist_ok=True)
        
        # Initialize database
        self.init_database()
    
    def init_database(self):
        """Initialize database tables"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # Tasks table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS tasks (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    app_name TEXT NOT NULL,
                    package_name TEXT NOT NULL,
                    task_name TEXT NOT NULL,
                    click_type TEXT NOT NULL,
                    image_paths TEXT,  -- JSON array of image paths
                    device_id TEXT,
                    emulator_name TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    is_active BOOLEAN DEFAULT 1
                )
            ''')
            
            # Emulators table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS emulators (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL UNIQUE,
                    adb_path TEXT NOT NULL,
                    device_id TEXT,
                    port INTEGER,
                    status TEXT DEFAULT 'disconnected',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    last_connected TIMESTAMP
                )
            ''')
            
            # App info cache table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS app_cache (
                    package_name TEXT PRIMARY KEY,
                    app_name TEXT NOT NULL,
                    icon_path TEXT,
                    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            conn.commit()
    
    def save_task(self, task_config: Dict[str, Any]) -> int:
        """Save task configuration to database"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            # Handle both single image_path and multiple image_paths
            if 'image_path' in task_config:
                # New format: single image per task
                image_paths_json = json.dumps([task_config['image_path']])
            else:
                # Old format: multiple images per task
                image_paths_json = json.dumps(task_config.get('image_paths', []))

            cursor.execute('''
                INSERT INTO tasks (
                    app_name, package_name, task_name, click_type,
                    image_paths, device_id, emulator_name
                ) VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (
                task_config['app_name'],
                task_config['package_name'],
                task_config['task_name'],
                task_config['click_type'],
                image_paths_json,
                task_config.get('device_id', ''),
                task_config.get('emulator_name', '')
            ))

            task_id = cursor.lastrowid
            conn.commit()
            return task_id
    
    def get_tasks(self, package_name: str = None, app_name: str = None, emulator_name: str = None) -> List[Dict[str, Any]]:
        """Get tasks from database with optional filters"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            query = '''
                SELECT id, app_name, package_name, task_name, click_type,
                       image_paths, device_id, emulator_name, created_at, updated_at
                FROM tasks
                WHERE is_active = 1
            '''
            params = []

            if package_name:
                query += ' AND package_name = ?'
                params.append(package_name)

            if app_name:
                query += ' AND app_name = ?'
                params.append(app_name)

            if emulator_name:
                query += ' AND emulator_name = ?'
                params.append(emulator_name)
            
            query += ' ORDER BY created_at DESC'
            
            cursor.execute(query, params)
            rows = cursor.fetchall()
            
            tasks = []
            for row in rows:
                task = {
                    'id': row[0],
                    'app_name': row[1],
                    'package_name': row[2],
                    'task_name': row[3],
                    'click_type': row[4],
                    'image_paths': json.loads(row[5]) if row[5] else [],
                    'device_id': row[6],
                    'emulator_name': row[7],
                    'created_at': row[8],
                    'updated_at': row[9]
                }
                tasks.append(task)
            
            return tasks
    
    def delete_task(self, task_id: int) -> bool:
        """Delete task (soft delete by setting is_active = 0)"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                UPDATE tasks SET is_active = 0, updated_at = CURRENT_TIMESTAMP 
                WHERE id = ?
            ''', (task_id,))
            conn.commit()
            return cursor.rowcount > 0
    
    def save_emulator(self, emulator_config: Dict[str, Any]) -> int:
        """Save emulator configuration"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT OR REPLACE INTO emulators (
                    name, adb_path, device_id, port, status
                ) VALUES (?, ?, ?, ?, ?)
            ''', (
                emulator_config['name'],
                emulator_config['adb_path'],
                emulator_config.get('device_id', ''),
                emulator_config.get('port', 5555),
                emulator_config.get('status', 'disconnected')
            ))
            
            emulator_id = cursor.lastrowid
            conn.commit()
            return emulator_id
    
    def get_emulators(self) -> List[Dict[str, Any]]:
        """Get all emulators from database"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT id, name, adb_path, device_id, port, status, last_connected
                FROM emulators 
                ORDER BY name
            ''')
            rows = cursor.fetchall()
            
            emulators = []
            for row in rows:
                emulator = {
                    'id': row[0],
                    'name': row[1],
                    'adb_path': row[2],
                    'device_id': row[3],
                    'port': row[4],
                    'status': row[5],
                    'last_connected': row[6]
                }
                emulators.append(emulator)
            
            return emulators
    
    def update_emulator_status(self, emulator_id: int, status: str, device_id: str = None):
        """Update emulator connection status"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            if device_id:
                cursor.execute('''
                    UPDATE emulators 
                    SET status = ?, device_id = ?, last_connected = CURRENT_TIMESTAMP
                    WHERE id = ?
                ''', (status, device_id, emulator_id))
            else:
                cursor.execute('''
                    UPDATE emulators 
                    SET status = ?
                    WHERE id = ?
                ''', (status, emulator_id))
            
            conn.commit()
    
    def cache_app_info(self, package_name: str, app_name: str, icon_path: str = None):
        """Cache app information for faster display"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT OR REPLACE INTO app_cache (package_name, app_name, icon_path)
                VALUES (?, ?, ?)
            ''', (package_name, app_name, icon_path))
            conn.commit()
    
    def get_cached_app_info(self, package_name: str) -> Optional[Dict[str, str]]:
        """Get cached app information"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT app_name, icon_path FROM app_cache WHERE package_name = ?
            ''', (package_name,))
            row = cursor.fetchone()
            
            if row:
                return {
                    'app_name': row[0],
                    'icon_path': row[1]
                }
            return None
    
    def get_task_count_by_app(self) -> Dict[str, int]:
        """Get task count grouped by app"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT package_name, COUNT(*) as task_count
                FROM tasks 
                WHERE is_active = 1
                GROUP BY package_name
            ''')
            rows = cursor.fetchall()
            
            return {row[0]: row[1] for row in rows}
